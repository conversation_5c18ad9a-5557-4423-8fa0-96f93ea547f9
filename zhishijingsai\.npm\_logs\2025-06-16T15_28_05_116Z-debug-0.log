0 verbose cli /usr/bin/node /usr/lib/node_modules/npm/bin/npm-cli.js
1 info using npm@8.19.4
2 info using node@v16.20.2
3 timing npm:load:whichnode Completed in 0ms
4 timing config:load:defaults Completed in 2ms
5 timing config:load:file:/usr/lib/node_modules/npm/npmrc Completed in 0ms
6 timing config:load:builtin Completed in 1ms
7 timing config:load:cli Completed in 1ms
8 timing config:load:env Completed in 0ms
9 timing config:load:file:/home/<USER>/socket-server/.npmrc Completed in 0ms
10 timing config:load:project Completed in 12ms
11 timing config:load:file:/home/<USER>/.npmrc Completed in 0ms
12 timing config:load:user Completed in 0ms
13 timing config:load:file:/usr/etc/npmrc Completed in 1ms
14 timing config:load:global Completed in 1ms
15 timing config:load:validate Completed in 0ms
16 timing config:load:credentials Completed in 1ms
17 timing config:load:setEnvs Completed in 0ms
18 timing config:load Completed in 18ms
19 timing npm:load:configload Completed in 18ms
20 timing npm:load:mkdirpcache Completed in 1ms
21 timing npm:load:mkdirplogs Completed in 0ms
22 verbose title npm exec pm2 start server.js --name socket-server
23 verbose argv "exec" "--" "pm2" "start" "server.js" "--name" "socket-server"
24 timing npm:load:setTitle Completed in 1ms
25 timing config:load:flatten Completed in 3ms
26 timing npm:load:display Completed in 4ms
27 verbose logfile logs-max:10 dir:/home/<USER>/.npm/_logs
28 verbose logfile /home/<USER>/.npm/_logs/2025-06-16T15_28_05_116Z-debug-0.log
29 timing npm:load:logFile Completed in 4ms
30 timing npm:load:timers Completed in 0ms
31 timing npm:load:configScope Completed in 0ms
32 timing npm:load Completed in 29ms
33 silly logfile done cleaning log files
34 timing command:exec Completed in 517ms
35 verbose exit 0
36 timing npm Completed in 608ms
37 info ok
