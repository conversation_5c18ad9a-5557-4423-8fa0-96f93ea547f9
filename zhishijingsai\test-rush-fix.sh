#!/bin/bash

# 测试抢答修复效果的脚本

echo "=== 测试抢答修复效果 ==="

PROJECT_PATH="/home/<USER>"

# 1. 检查服务状态
echo "1. 检查服务状态..."
cd "$PROJECT_PATH"

# 检查Nginx
if systemctl is-active --quiet nginx; then
    echo "✅ Nginx 运行正常"
else
    echo "❌ Nginx 未运行"
fi

# 检查Node.js服务
if pgrep -f "node.*server.js" > /dev/null; then
    echo "✅ Socket.IO服务运行正常"
else
    echo "❌ Socket.IO服务未运行"
fi

# 2. 检查关键文件
echo ""
echo "2. 检查关键文件..."

# 检查socket-client.js是否包含新的事件处理
if grep -q "rush-reset" "$PROJECT_PATH/socket-client.js"; then
    echo "✅ socket-client.js 包含抢答重置逻辑"
else
    echo "❌ socket-client.js 缺少抢答重置逻辑"
fi

if grep -q "handleRushReset" "$PROJECT_PATH/socket-client.js"; then
    echo "✅ socket-client.js 包含重置处理函数"
else
    echo "❌ socket-client.js 缺少重置处理函数"
fi

# 检查服务器端是否包含重置逻辑
if grep -q "rush-reset" "$PROJECT_PATH/socket-server/server.js"; then
    echo "✅ server.js 包含抢答重置逻辑"
else
    echo "❌ server.js 缺少抢答重置逻辑"
fi

# 3. 测试网站访问
echo ""
echo "3. 测试网站访问..."

# 获取服务器IP
SERVER_IP=$(hostname -I | awk '{print $1}')

# 测试主页
if curl -s -o /dev/null -w "%{http_code}" "http://$SERVER_IP" | grep -q "200"; then
    echo "✅ 主页访问正常"
else
    echo "❌ 主页访问失败"
fi

# 测试抢答页面
if curl -s -o /dev/null -w "%{http_code}" "http://$SERVER_IP/rush.html" | grep -q "200"; then
    echo "✅ 抢答页面访问正常"
else
    echo "❌ 抢答页面访问失败"
fi

# 4. 检查Socket.IO连接
echo ""
echo "4. 检查Socket.IO连接..."

# 检查Socket.IO端口
if netstat -tlnp | grep -q ":3000"; then
    echo "✅ Socket.IO端口3000监听正常"
else
    echo "❌ Socket.IO端口3000未监听"
fi

# 5. 显示测试指南
echo ""
echo "=== 测试指南 ==="
echo ""
echo "请按以下步骤测试抢答修复效果："
echo ""
echo "📱 **步骤1：准备两个设备**"
echo "   - 设备A：用户123"
echo "   - 设备B：用户456"
echo ""
echo "📱 **步骤2：同时进入抢答页面**"
echo "   - 两个设备都访问：http://$SERVER_IP/rush.html"
echo "   - 分别输入用户名123和456"
echo "   - 点击'开始答题'"
echo ""
echo "📱 **步骤3：测试第一题抢答**"
echo "   - 设备A（123）点击'抢答'按钮"
echo "   - 观察设备B（456）是否显示'123 抢答成功，请等待作答完成'"
echo "   - 设备A选择答案并提交"
echo ""
echo "📱 **步骤4：观察自动跳转**"
echo "   - 答题完成后，两个设备都应该显示答题结果"
echo "   - 3秒后，两个设备都应该自动进入第二题抢答"
echo "   - 设备B（456）应该能够正常参与第二题抢答"
echo ""
echo "📱 **步骤5：测试第二题抢答**"
echo "   - 设备B（456）点击'抢答'按钮"
echo "   - 观察设备A（123）是否显示'456 抢答成功，请等待作答完成'"
echo "   - 设备B选择答案并提交"
echo ""
echo "📱 **步骤6：验证完整流程**"
echo "   - 两题抢答完成后，应该自动跳转到module2.html"
echo "   - 整个过程中没有用户卡在等待界面"
echo ""
echo "🔍 **预期结果：**"
echo "   ✅ 没有抢到答题权的用户能正常参与下一题"
echo "   ✅ 答题完成后所有用户状态正确重置"
echo "   ✅ 自动进入下一题抢答，无需手动刷新"
echo "   ✅ 抢答题全部完成后正确跳转到下一模块"
echo ""
echo "❌ **如果仍有问题，请提供：**"
echo "   1. 具体在哪一步出现问题"
echo "   2. 浏览器控制台的错误信息"
echo "   3. 两个用户的具体表现"
echo ""
echo "🌐 **访问地址：**"
echo "   主页：http://$SERVER_IP"
echo "   抢答页面：http://$SERVER_IP/rush.html"
echo ""
echo "=== 开始测试吧！ ==="
