const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const fs = require('fs');
const path = require('path');

// 创建Express应用
const app = express();
const server = http.createServer(app);

// 创建Socket.IO服务器
const io = socketIo(server);

// 存储房间状态
let rooms = {};

// 从文件加载房间状态（如果存在）
function loadRoomsFromFile() {
    try {
        const roomsData = fs.readFileSync(path.join(__dirname, 'rooms.json'), 'utf8');
        rooms = JSON.parse(roomsData);
        console.log('房间状态已从文件加载');
    } catch (error) {
        console.log('无法加载房间状态文件，使用空房间状态');
        rooms = {};
    }
}

// 保存房间状态到文件
function saveRoomsToFile() {
    try {
        fs.writeFileSync(path.join(__dirname, 'rooms.json'), JSON.stringify(rooms, null, 2), 'utf8');
        console.log('房间状态已保存到文件');
    } catch (error) {
        console.error('保存房间状态失败:', error);
    }
}

// 尝试加载房间状态
loadRoomsFromFile();

// 定期保存房间状态（每5分钟）
setInterval(saveRoomsToFile, 5 * 60 * 1000);

// Socket.IO连接处理
io.on('connection', (socket) => {
    console.log('新用户连接:', socket.id);
    
    // 用户加入房间
    socket.on('join-room', (data) => {
        const { roomId, userId, userName } = data;
        
        console.log(`用户 ${userName}(${userId}) 加入房间 ${roomId}`);
        
        // 检查房间是否存在
        if (!rooms[roomId]) {
            // 如果房间不存在，创建房间
            rooms[roomId] = {
                users: {},
                rushWinner: null,
                isRushMode: false,
                currentQuestion: null,
                lastUpdated: Date.now()
            };
        }
        
        // 将用户添加到房间
        rooms[roomId].users[userId] = {
            socketId: socket.id,
            name: userName,
            joinTime: Date.now()
        };
        
        // 将Socket加入房间
        socket.join(roomId);
        
        // 通知房间内所有用户有新用户加入
        io.to(roomId).emit('user-joined', {
            userId: userId,
            userName: userName,
            totalUsers: Object.keys(rooms[roomId].users).length
        });
        
        // 如果房间处于抢答模式，通知新用户
        if (rooms[roomId].isRushMode) {
            socket.emit('rush-status', {
                isRushMode: true,
                winnerId: rooms[roomId].rushWinner,
                winnerName: rooms[roomId].users[rooms[roomId].rushWinner]?.name || '未知用户'
            });
        }
        
        // 更新房间最后活动时间
        rooms[roomId].lastUpdated = Date.now();
        
        // 保存房间状态
        saveRoomsToFile();
    });
    
    // ===== 模块一抢答事件处理 =====
    
    // 用户尝试抢答
    socket.on('rush-attempt', (data) => {
        const { roomId, userId, userName } = data;
        
        console.log(`用户 ${userName}(${userId}) 尝试在房间 ${roomId} 抢答`);
        
        // 检查房间是否存在
        if (!rooms[roomId]) {
            // 如果房间不存在，创建房间
            rooms[roomId] = {
                users: {},
                rushWinner: null,
                isRushMode: false,
                currentQuestion: null,
                lastUpdated: Date.now()
            };
        }
        
        // 检查房间是否已经处于抢答模式
        if (rooms[roomId].isRushMode) {
            console.log(`房间 ${roomId} 已经处于抢答模式，抢答者: ${rooms[roomId].users[rooms[roomId].rushWinner]?.name}`);
            
            // 通知当前用户抢答失败
            socket.emit('rush-result', {
                winnerId: rooms[roomId].rushWinner,
                winnerName: rooms[roomId].users[rooms[roomId].rushWinner]?.name || '未知用户'
            });
            
            return;
        }
        
        // 将用户添加到房间（如果尚未添加）
        if (!rooms[roomId].users[userId]) {
            rooms[roomId].users[userId] = {
                socketId: socket.id,
                name: userName,
                joinTime: Date.now()
            };
            
            // 将Socket加入房间
            socket.join(roomId);
        }
        
        // 设置房间为抢答模式，并记录抢答成功者
        rooms[roomId].isRushMode = true;
        rooms[roomId].rushWinner = userId;
        rooms[roomId].lastUpdated = Date.now();
        
        console.log(`用户 ${userName}(${userId}) 在房间 ${roomId} 抢答成功`);
        
        // 通知房间内所有用户抢答结果
        io.to(roomId).emit('rush-result', {
            winnerId: userId,
            winnerName: userName
        });
        
        // 保存房间状态
        saveRoomsToFile();
    });
    
    // 用户提交抢答答案
    socket.on('rush-answer-submitted', (data) => {
        const { roomId, userId, userName, questionId, answer, isCorrect, score } = data;
        
        console.log(`用户 ${userName}(${userId}) 在房间 ${roomId} 提交了答案`);
        
        // 检查房间是否存在
        if (!rooms[roomId]) {
            console.log(`房间 ${roomId} 不存在`);
            socket.emit('error', { message: '房间不存在' });
            return;
        }
        
        // 检查用户是否是抢答成功者
        if (rooms[roomId].rushWinner !== userId) {
            console.log(`用户 ${userName}(${userId}) 不是抢答成功者`);
            socket.emit('error', { message: '您不是抢答成功者' });
            return;
        }
        
        console.log(`用户 ${userName}(${userId}) 在房间 ${roomId} 提交了答案: ${answer}, 正确: ${isCorrect}, 得分: ${score}`);
        
        // 重置房间状态
        rooms[roomId].isRushMode = false;
        rooms[roomId].rushWinner = null;
        rooms[roomId].lastUpdated = Date.now();
        
        // 通知房间内所有用户答题完成
        io.to(roomId).emit('rush-answer-complete', {
            userId: userId,
            userName: userName,
            questionId: questionId,
            isCorrect: isCorrect,
            score: score
        });
        
        // 保存房间状态
        saveRoomsToFile();
    });
    
    // ===== 模块三抢答事件处理 =====
    
    // 用户尝试模块三抢答
    socket.on('module3-rush-attempt', (data) => {
        const { roomId, userId, userName } = data;
        
        console.log(`用户 ${userName}(${userId}) 尝试在房间 ${roomId} 模块三抢答`);
        
        // 检查房间是否存在
        if (!rooms[roomId]) {
            // 如果房间不存在，创建房间
            rooms[roomId] = {
                users: {},
                rushWinner: null,
                isRushMode: false,
                currentQuestion: null,
                lastUpdated: Date.now()
            };
        }
        
        // 检查房间是否已经处于抢答模式
        if (rooms[roomId].isRushMode) {
            console.log(`房间 ${roomId} 已经处于抢答模式，抢答者: ${rooms[roomId].users[rooms[roomId].rushWinner]?.name}`);
            
            // 通知当前用户抢答失败
            socket.emit('module3-rush-result', {
                winnerId: rooms[roomId].rushWinner,
                winnerName: rooms[roomId].users[rooms[roomId].rushWinner]?.name || '未知用户'
            });
            
            return;
        }
        
        // 将用户添加到房间（如果尚未添加）
        if (!rooms[roomId].users[userId]) {
            rooms[roomId].users[userId] = {
                socketId: socket.id,
                name: userName,
                joinTime: Date.now()
            };
            
            // 将Socket加入房间
            socket.join(roomId);
        }
        
        // 设置房间为抢答模式，并记录抢答成功者
        rooms[roomId].isRushMode = true;
        rooms[roomId].rushWinner = userId;
        rooms[roomId].lastUpdated = Date.now();
        
        console.log(`用户 ${userName}(${userId}) 在房间 ${roomId} 模块三抢答成功`);
        
        // 通知房间内所有用户抢答结果
        io.to(roomId).emit('module3-rush-result', {
            winnerId: userId,
            winnerName: userName
        });
        
        // 保存房间状态
        saveRoomsToFile();
    });
    
    // 用户提交模块三抢答答案
    socket.on('module3-answer-submitted', (data) => {
        const { roomId, userId, userName, questionId, answer, isCorrect, score } = data;
        
        console.log(`用户 ${userName}(${userId}) 在房间 ${roomId} 提交了模块三答案`);
        
        // 检查房间是否存在
        if (!rooms[roomId]) {
            console.log(`房间 ${roomId} 不存在`);
            socket.emit('error', { message: '房间不存在' });
            return;
        }
        
        // 检查用户是否是抢答成功者
        if (rooms[roomId].rushWinner !== userId) {
            console.log(`用户 ${userName}(${userId}) 不是抢答成功者`);
            socket.emit('error', { message: '您不是抢答成功者' });
            return;
        }
        
        console.log(`用户 ${userName}(${userId}) 在房间 ${roomId} 提交了模块三答案: ${answer}, 正确: ${isCorrect}, 得分: ${score}`);
        
        // 重置房间状态
        rooms[roomId].isRushMode = false;
        rooms[roomId].rushWinner = null;
        rooms[roomId].lastUpdated = Date.now();
        
        // 通知房间内所有用户答题完成
        io.to(roomId).emit('module3-answer-complete', {
            userId: userId,
            userName: userName,
            questionId: questionId,
            isCorrect: isCorrect,
            score: score
        });
        
        // 保存房间状态
        saveRoomsToFile();
    });
    
    // 用户断开连接
    socket.on('disconnect', () => {
        console.log('用户断开连接:', socket.id);
        
        // 从所有房间中移除用户
        for (const roomId in rooms) {
            const room = rooms[roomId];
            
            for (const userId in room.users) {
                if (room.users[userId].socketId === socket.id) {
                    const userName = room.users[userId].name;
                    console.log(`用户 ${userName}(${userId}) 离开房间 ${roomId}`);
                    
                    // 如果断开连接的用户是抢答成功者，重置房间状态
                    if (room.isRushMode && room.rushWinner === userId) {
                        room.isRushMode = false;
                        room.rushWinner = null;
                        
                        // 通知房间内所有用户抢答状态已重置
                        io.to(roomId).emit('rush-reset', {
                            message: '抢答者已断开连接，抢答状态已重置'
                        });
                    }
                    
                    // 从房间中移除用户
                    delete room.users[userId];
                    
                    // 通知房间内所有用户有用户离开
                    io.to(roomId).emit('user-left', {
                        userId: userId,
                        userName: userName,
                        totalUsers: Object.keys(room.users).length
                    });
                    
                    // 更新房间最后活动时间
                    room.lastUpdated = Date.now();
                    
                    break;
                }
            }
        }
        
        // 保存房间状态
        saveRoomsToFile();
    });
});

// 设置静态文件目录
app.use(express.static(path.join(__dirname, '../')));

// 启动服务器
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`服务器运行在端口 ${PORT}`);
});
