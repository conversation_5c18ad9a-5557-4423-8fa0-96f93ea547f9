#!/bin/bash

# 修复抢答逻辑问题的脚本

echo "=== 修复抢答逻辑问题 ==="

PROJECT_PATH="/home/<USER>"

# 1. 备份当前文件
echo "1. 备份当前文件..."
sudo cp "$PROJECT_PATH/script.js" "$PROJECT_PATH/script.js.backup-rush-$(date +%Y%m%d-%H%M%S)"
sudo cp "$PROJECT_PATH/socket-client.js" "$PROJECT_PATH/socket-client.js.backup-rush-$(date +%Y%m%d-%H%M%S)"
sudo cp "$PROJECT_PATH/socket-server/server.js" "$PROJECT_PATH/socket-server/server.js.backup-rush-$(date +%Y%m%d-%H%M%S)"

echo "✅ 文件备份完成"

# 2. 修复socket-client.js - 添加答题完成后的重置逻辑
echo "2. 修复socket-client.js..."

cat > /tmp/socket-client-fix.js << 'EOF'
// Socket.IO客户端管理
let socket = null;
let isConnected = false;

// 初始化Socket.IO连接
function initSocket() {
    // 如果已经连接，则不再重复连接
    if (isConnected && socket) return;
    
    // 连接到Socket.IO服务器 - 通过Nginx代理
    socket = io();
    
    // 连接成功事件
    socket.on('connect', () => {
        console.log('Socket.IO连接成功');
        isConnected = true;
        
        // 如果当前用户存在，自动加入房间
        if (currentUser) {
            joinRoom();
        }
    });
    
    // 连接断开事件
    socket.on('disconnect', () => {
        console.log('Socket.IO连接断开');
        isConnected = false;
    });
    
    // 用户加入房间事件
    socket.on('user-joined', (data) => {
        console.log('用户加入房间:', data);
        // 可以在这里更新在线用户列表
    });
    
    // 用户离开房间事件
    socket.on('user-left', (data) => {
        console.log('用户离开房间:', data);
        // 可以在这里更新在线用户列表
    });
    
    // 抢答结果事件
    socket.on('rush-result', (data) => {
        console.log('收到抢答结果:', data);
        handleRushResult(data);
    });
    
    // 抢答重置事件 - 新增
    socket.on('rush-reset', (data) => {
        console.log('收到抢答重置事件:', data);
        handleRushReset(data);
    });
    
    // 答题完成事件
    socket.on('rush-answer-complete', (data) => {
        console.log('收到答题完成事件:', data);
        handleRushAnswerComplete(data);
    });
    
    // 模块三抢答结果事件
    socket.on('module3-rush-result', (data) => {
        console.log('收到模块三抢答结果:', data);
        handleModule3RushResult(data);
    });
    
    // 模块三答题完成事件
    socket.on('module3-answer-complete', (data) => {
        console.log('收到模块三答题完成事件:', data);
        handleModule3AnswerComplete(data);
    });
    
    // 模块三抢答重置事件 - 新增
    socket.on('module3-rush-reset', (data) => {
        console.log('收到模块三抢答重置事件:', data);
        handleModule3RushReset(data);
    });
    
    // 错误事件
    socket.on('error', (data) => {
        console.error('Socket.IO错误:', data);
        alert('连接错误: ' + data.message);
    });
}

// 加入房间
function joinRoom() {
    if (!socket || !currentUser) return;
    
    const roomId = getCurrentRoomId();
    
    socket.emit('join-room', {
        roomId: roomId,
        userId: currentUser.id,
        userName: currentUser.name
    });
    
    console.log('已发送加入房间请求:', roomId);
}

// 获取当前房间ID
function getCurrentRoomId() {
    const currentPage = window.location.pathname;
    
    if (currentPage.includes('rush.html')) {
        return 'default-room';
    } else if (currentPage.includes('module3.html')) {
        return 'module3-room';
    }
    
    return 'default-room';
}

// 处理抢答结果
function handleRushResult(data) {
    const { winnerId, winnerName } = data;
    
    if (winnerId === currentUser.id) {
        // 当前用户抢答成功
        console.log('抢答成功！');
        
        // 更新状态
        isRushMode = true;
        rushWinner = winnerId;
        isWaitingForWinner = false;
        hasRushed = true;
        
        // 更新UI
        const statusText = document.getElementById('status-text');
        if (statusText) {
            statusText.textContent = '抢答成功，请作答';
        }
        
        // 隐藏抢答按钮，显示提交按钮
        const rushBtn = document.getElementById('rush-btn');
        const submitBtn = document.getElementById('rush-submit-btn');
        
        if (rushBtn && submitBtn) {
            rushBtn.style.display = 'none';
            submitBtn.style.display = 'block';
        }
        
    } else {
        // 其他用户抢答成功
        console.log(`用户 ${winnerName} 抢答成功`);
        
        // 更新状态
        isRushMode = true;
        rushWinner = winnerId;
        isWaitingForWinner = true;
        
        // 更新UI
        const statusText = document.getElementById('status-text');
        if (statusText) {
            statusText.textContent = `${winnerName} 抢答成功，请等待作答完成`;
        }
        
        // 禁用抢答按钮
        const rushBtn = document.getElementById('rush-btn');
        if (rushBtn) {
            rushBtn.disabled = true;
            rushBtn.textContent = '等待中...';
        }
    }
}

// 处理抢答重置 - 新增
function handleRushReset(data) {
    console.log('抢答重置:', data.reason);
    
    // 重置所有抢答相关状态
    isRushMode = false;
    rushWinner = null;
    isWaitingForWinner = false;
    hasRushed = false;
    
    // 更新UI
    const statusText = document.getElementById('status-text');
    if (statusText) {
        statusText.textContent = '准备抢答';
    }
    
    // 恢复抢答按钮
    const rushBtn = document.getElementById('rush-btn');
    const submitBtn = document.getElementById('rush-submit-btn');
    
    if (rushBtn) {
        rushBtn.disabled = false;
        rushBtn.style.display = 'block';
        rushBtn.textContent = '抢答';
    }
    
    if (submitBtn) {
        submitBtn.style.display = 'none';
    }
}

// 处理答题完成 - 修改
function handleRushAnswerComplete(data) {
    console.log('答题完成:', data);
    
    const { userId, userName, isCorrect, score } = data;
    
    // 显示答题结果
    const message = `${userName} 答题完成！${isCorrect ? '回答正确' : '回答错误'}，得分：${score}`;
    
    // 重置抢答状态，准备下一题
    setTimeout(() => {
        // 检查是否还有更多抢答题
        if (currentRushIndex + 1 < rushQuestions.length) {
            // 还有题目，进入下一题
            currentRushIndex++;
            
            // 重置状态
            isRushMode = false;
            rushWinner = null;
            isWaitingForWinner = false;
            hasRushed = false;
            selectedAnswer = null;
            
            // 显示下一题
            showRushQuestion(rushQuestions[currentRushIndex]);
            updateRushProgress();
            
            // 更新状态文本
            const statusText = document.getElementById('status-text');
            if (statusText) {
                statusText.textContent = '准备抢答下一题';
            }
            
            // 恢复抢答按钮
            const rushBtn = document.getElementById('rush-btn');
            const submitBtn = document.getElementById('rush-submit-btn');
            
            if (rushBtn) {
                rushBtn.disabled = false;
                rushBtn.style.display = 'block';
                rushBtn.textContent = '抢答';
            }
            
            if (submitBtn) {
                submitBtn.style.display = 'none';
            }
            
        } else {
            // 抢答题目全部完成，跳转到下一模块
            window.location.href = 'module2.html';
        }
    }, 3000); // 3秒后自动进入下一题
}

// 模块三相关函数...
function handleModule3RushResult(data) {
    const { winnerId, winnerName } = data;
    
    if (winnerId === currentUser.id) {
        // 当前用户抢答成功
        isModule3RushMode = true;
        module3RushWinner = winnerId;
        isWaitingForModule3Winner = false;
        hasModule3Rushed = true;
        
        const statusText = document.getElementById('status-text');
        if (statusText) {
            statusText.textContent = '抢答成功，请作答';
        }
        
        const rushBtn = document.getElementById('module3-rush-btn');
        const submitBtn = document.getElementById('module3-submit-btn');
        
        if (rushBtn && submitBtn) {
            rushBtn.style.display = 'none';
            submitBtn.style.display = 'block';
        }
        
        // 播放音频
        const audioElement = document.querySelector('.question-audio');
        if (audioElement) {
            audioElement.play();
        }
        
    } else {
        // 其他用户抢答成功
        isModule3RushMode = true;
        module3RushWinner = winnerId;
        isWaitingForModule3Winner = true;
        
        const statusText = document.getElementById('status-text');
        if (statusText) {
            statusText.textContent = `${winnerName} 抢答成功，请等待作答完成`;
        }
        
        const rushBtn = document.getElementById('module3-rush-btn');
        if (rushBtn) {
            rushBtn.disabled = true;
            rushBtn.textContent = '等待中...';
        }
    }
}

function handleModule3RushReset(data) {
    console.log('模块三抢答重置:', data.reason);
    
    isModule3RushMode = false;
    module3RushWinner = null;
    isWaitingForModule3Winner = false;
    hasModule3Rushed = false;
    
    const statusText = document.getElementById('status-text');
    if (statusText) {
        statusText.textContent = '准备抢答红歌题（第一个抢答的人才能作答）';
    }
    
    const rushBtn = document.getElementById('module3-rush-btn');
    const submitBtn = document.getElementById('module3-submit-btn');
    
    if (rushBtn) {
        rushBtn.disabled = false;
        rushBtn.style.display = 'block';
        rushBtn.textContent = '抢答';
    }
    
    if (submitBtn) {
        submitBtn.style.display = 'none';
    }
}

function handleModule3AnswerComplete(data) {
    console.log('模块三答题完成:', data);
    
    const { userId, userName, isCorrect, score } = data;
    
    setTimeout(() => {
        if (currentModule3Index + 1 < 3) {
            // 还有题目，进入下一题
            currentModule3Index++;
            
            // 重置状态
            isModule3RushMode = false;
            module3RushWinner = null;
            isWaitingForModule3Winner = false;
            hasModule3Rushed = false;
            selectedAnswer = null;
            
            // 显示下一题
            showModule3Question(module3Questions[currentModule3Index]);
            updateModule3Progress();
            
            const statusText = document.getElementById('status-text');
            if (statusText) {
                statusText.textContent = '准备抢答下一题红歌';
            }
            
            const rushBtn = document.getElementById('module3-rush-btn');
            const submitBtn = document.getElementById('module3-submit-btn');
            
            if (rushBtn) {
                rushBtn.disabled = false;
                rushBtn.style.display = 'block';
                rushBtn.textContent = '抢答';
            }
            
            if (submitBtn) {
                submitBtn.style.display = 'none';
            }
            
        } else {
            // 模块三完成，跳转到下一模块
            window.location.href = 'module4.html';
        }
    }, 3000);
}

// 页面加载时初始化Socket连接
document.addEventListener('DOMContentLoaded', () => {
    // 延迟初始化，确保其他脚本已加载
    setTimeout(initSocket, 1000);
});
EOF

# 复制修复的文件
sudo cp /tmp/socket-client-fix.js "$PROJECT_PATH/socket-client.js"
sudo chown www-data:www-data "$PROJECT_PATH/socket-client.js"
sudo chmod 644 "$PROJECT_PATH/socket-client.js"

echo "✅ socket-client.js 修复完成"

# 3. 修复服务器端逻辑
echo "3. 修复服务器端逻辑..."

# 在服务器端添加答题完成后的重置和下一题逻辑
sudo sed -i '/rush-answer-complete/a\
        \
        // 3秒后重置房间状态，准备下一题\
        setTimeout(() => {\
            room.isRushMode = false;\
            room.rushWinner = null;\
            room.lastUpdated = Date.now();\
            \
            // 通知房间内所有用户重置抢答状态\
            io.to(roomId).emit("rush-reset", {\
                reason: "准备下一题抢答"\
            });\
            \
            saveRoomsToFile();\
        }, 3000);' "$PROJECT_PATH/socket-server/server.js"

sudo sed -i '/module3-answer-complete/a\
        \
        // 3秒后重置房间状态，准备下一题\
        setTimeout(() => {\
            room.isRushMode = false;\
            room.rushWinner = null;\
            room.lastUpdated = Date.now();\
            \
            // 通知房间内所有用户重置抢答状态\
            io.to(roomId).emit("module3-rush-reset", {\
                reason: "准备下一题抢答"\
            });\
            \
            saveRoomsToFile();\
        }, 3000);' "$PROJECT_PATH/socket-server/server.js"

echo "✅ 服务器端逻辑修复完成"

# 4. 重启服务
echo "4. 重启服务..."
cd "$PROJECT_PATH"
./stop-services.sh
sleep 2
./start-services.sh

echo ""
echo "=== 抢答逻辑修复完成 ==="
echo ""
echo "修复内容："
echo "1. ✅ 添加了答题完成后的自动重置机制"
echo "2. ✅ 实现了自动进入下一题抢答的逻辑"
echo "3. ✅ 修复了用户状态同步问题"
echo "4. ✅ 添加了3秒延迟确保用户看到结果"
echo ""
echo "现在的流程："
echo "1. 用户抢答第一题"
echo "2. 抢到的用户答题，其他用户等待"
echo "3. 答题完成后，所有用户自动重置状态"
echo "4. 3秒后自动进入第二题抢答"
echo "5. 重复流程直到所有抢答题完成"
echo ""
echo "请测试多用户抢答功能！"
