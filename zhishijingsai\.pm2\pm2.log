2025-06-16T23:28:05: PM2 log: ===============================================================================
2025-06-16T23:28:05: PM2 log: --- New PM2 Daemon started ----------------------------------------------------
2025-06-16T23:28:05: PM2 log: Time                 : Mon Jun 16 2025 23:28:05 GMT+0800 (China Standard Time)
2025-06-16T23:28:05: PM2 log: PM2 version          : 6.0.8
2025-06-16T23:28:05: PM2 log: Node.js version      : 16.20.2
2025-06-16T23:28:05: PM2 log: Current arch         : x64
2025-06-16T23:28:05: PM2 log: PM2 home             : /home/<USER>/.pm2
2025-06-16T23:28:05: PM2 log: PM2 PID file         : /home/<USER>/.pm2/pm2.pid
2025-06-16T23:28:05: PM2 log: RPC socket file      : /home/<USER>/.pm2/rpc.sock
2025-06-16T23:28:05: PM2 log: BUS socket file      : /home/<USER>/.pm2/pub.sock
2025-06-16T23:28:05: PM2 log: Application log path : /home/<USER>/.pm2/logs
2025-06-16T23:28:05: PM2 log: Worker Interval      : 30000
2025-06-16T23:28:05: PM2 log: Process dump file    : /home/<USER>/.pm2/dump.pm2
2025-06-16T23:28:05: PM2 log: Concurrent actions   : 2
2025-06-16T23:28:05: PM2 log: SIGTERM timeout      : 1600
2025-06-16T23:28:05: PM2 log: Runtime Binary       : /usr/bin/node
2025-06-16T23:28:05: PM2 log: ===============================================================================
2025-06-16T23:28:05: PM2 log: App [socket-server:0] starting in -fork mode-
2025-06-16T23:28:05: PM2 log: App [socket-server:0] online
2025-06-16T23:28:05: PM2 log: App [socket-server:0] exited with code [1] via signal [SIGINT]
2025-06-16T23:28:05: PM2 log: App [socket-server:0] starting in -fork mode-
2025-06-16T23:28:05: PM2 log: App [socket-server:0] online
2025-06-16T23:28:06: PM2 log: App [socket-server:0] exited with code [1] via signal [SIGINT]
2025-06-16T23:28:06: PM2 log: App [socket-server:0] starting in -fork mode-
2025-06-16T23:28:06: PM2 log: App [socket-server:0] online
2025-06-16T23:28:06: PM2 log: App [socket-server:0] exited with code [1] via signal [SIGINT]
2025-06-16T23:28:06: PM2 log: App [socket-server:0] starting in -fork mode-
2025-06-16T23:28:06: PM2 log: App [socket-server:0] online
2025-06-16T23:28:06: PM2 log: App [socket-server:0] exited with code [1] via signal [SIGINT]
2025-06-16T23:28:06: PM2 log: App [socket-server:0] starting in -fork mode-
2025-06-16T23:28:06: PM2 log: App [socket-server:0] online
2025-06-16T23:28:07: PM2 log: App [socket-server:0] exited with code [1] via signal [SIGINT]
2025-06-16T23:28:07: PM2 log: App [socket-server:0] starting in -fork mode-
2025-06-16T23:28:07: PM2 log: App [socket-server:0] online
2025-06-16T23:28:07: PM2 log: App [socket-server:0] exited with code [1] via signal [SIGINT]
2025-06-16T23:28:07: PM2 log: App [socket-server:0] starting in -fork mode-
2025-06-16T23:28:07: PM2 log: App [socket-server:0] online
2025-06-16T23:28:07: PM2 log: App [socket-server:0] exited with code [1] via signal [SIGINT]
2025-06-16T23:28:07: PM2 log: App [socket-server:0] starting in -fork mode-
2025-06-16T23:28:07: PM2 log: App [socket-server:0] online
2025-06-16T23:28:07: PM2 log: App [socket-server:0] exited with code [1] via signal [SIGINT]
2025-06-16T23:28:07: PM2 log: App [socket-server:0] starting in -fork mode-
2025-06-16T23:28:07: PM2 log: App [socket-server:0] online
2025-06-16T23:28:08: PM2 log: App [socket-server:0] exited with code [1] via signal [SIGINT]
2025-06-16T23:28:08: PM2 log: App [socket-server:0] starting in -fork mode-
2025-06-16T23:28:08: PM2 log: App [socket-server:0] online
2025-06-16T23:28:08: PM2 log: App [socket-server:0] exited with code [1] via signal [SIGINT]
2025-06-16T23:28:08: PM2 log: App [socket-server:0] starting in -fork mode-
2025-06-16T23:28:08: PM2 log: App [socket-server:0] online
2025-06-16T23:28:08: PM2 log: App [socket-server:0] exited with code [1] via signal [SIGINT]
2025-06-16T23:28:08: PM2 log: App [socket-server:0] starting in -fork mode-
2025-06-16T23:28:08: PM2 log: App [socket-server:0] online
2025-06-16T23:28:08: PM2 log: App [socket-server:0] exited with code [1] via signal [SIGINT]
2025-06-16T23:28:08: PM2 log: App [socket-server:0] starting in -fork mode-
2025-06-16T23:28:08: PM2 log: App [socket-server:0] online
2025-06-16T23:28:09: PM2 log: App [socket-server:0] exited with code [1] via signal [SIGINT]
2025-06-16T23:28:09: PM2 log: App [socket-server:0] starting in -fork mode-
2025-06-16T23:28:09: PM2 log: App [socket-server:0] online
2025-06-16T23:28:09: PM2 log: App [socket-server:0] exited with code [1] via signal [SIGINT]
2025-06-16T23:28:09: PM2 log: App [socket-server:0] starting in -fork mode-
2025-06-16T23:28:09: PM2 log: App [socket-server:0] online
2025-06-16T23:28:09: PM2 log: App [socket-server:0] exited with code [1] via signal [SIGINT]
2025-06-16T23:28:09: PM2 log: App [socket-server:0] starting in -fork mode-
2025-06-16T23:28:09: PM2 log: App [socket-server:0] online
2025-06-16T23:28:09: PM2 log: App [socket-server:0] exited with code [1] via signal [SIGINT]
2025-06-16T23:28:09: PM2 log: Script /home/<USER>/socket-server/server.js had too many unstable restarts (16). Stopped. "errored"
