<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>红歌竞猜</title>
    <link rel="stylesheet" href="styles.css">
    <!-- 引入Socket.IO客户端库 -->
    <script src="/socket.io/socket.io.js"></script>
    <!-- 引入自定义Socket客户端 -->
    <script src="socket-client.js"></script>

</head>
<body>
    <div class="container" id="module3-container">
        <div class="header">
            <h1>红歌竞猜</h1>
            <div class="user-info" id="user-info">
                <!-- 用户信息将通过JavaScript动态插入 -->
            </div>
        </div>

        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress" id="module3-progress"></div>
            </div>
            <div class="progress-text">
                <span id="current-module3-question">1</span>/<span id="total-module3-questions">3</span>
            </div>
        </div>

        <div class="status-container">
            <div class="status-text" id="status-text">准备抢答红歌题（第一个抢答的人才能作答）</div>
        </div>

        <div class="question-container" id="module3-question-container">
            <div class="audio-container" id="question-audio-container">
                <!-- 音频播放器将通过JavaScript动态插入 -->
            </div>
            
            <div class="question-content" id="module3-question-content">请听音频，选择正确的歌名</div>
            
            <div class="options-container" id="module3-options-container">
                <!-- 选项将通过JavaScript动态插入 -->
            </div>
        </div>

        <div class="action-container">
            <button id="module3-rush-btn" class="btn btn-primary">抢答</button>
            <button id="module3-submit-btn" class="btn btn-primary" style="display: none;">提交答案</button>
            <button id="module3-exit-btn" class="btn btn-secondary">退出答题</button>
        </div>

        <!-- 答题结果弹窗 -->
        <div class="modal" id="module3-result-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>答题结果</h2>
                </div>
                <div class="modal-body" id="module3-result-content">
                    <!-- 结果内容将通过JavaScript动态插入 -->
                </div>
                <div class="modal-footer">
                    <button id="module3-next-btn" class="btn btn-primary">继续</button>
                </div>
            </div>
        </div>

        <!-- 确认退出弹窗 -->
        <div class="modal" id="module3-exit-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>确认退出</h2>
                </div>
                <div class="modal-body">
                    <p>退出后答题进度将丢失，确定要退出吗？</p>
                </div>
                <div class="modal-footer">
                    <button id="module3-confirm-exit-btn" class="btn btn-danger">确认退出</button>
                    <button id="module3-cancel-exit-btn" class="btn btn-secondary">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script src="questions.js"></script>
    <script src="script.js"></script>
</body>
</html>
