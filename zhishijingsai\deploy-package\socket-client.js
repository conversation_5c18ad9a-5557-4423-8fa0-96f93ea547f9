// Socket.IO客户端
let socket;
let roomId = 'default-room'; // 默认房间ID
let isConnected = false;

// 初始化Socket.IO连接
function initSocket() {
    // 如果已经连接，则不再重复连接
    if (isConnected && socket) return;

    // 连接到Socket.IO服务器 - 通过Nginx代理
    socket = io();
    
    // 连接成功事件
    socket.on('connect', () => {
        console.log('已连接到Socket.IO服务器');
        isConnected = true;
        
        // 加入默认房间
        joinRoom(roomId, currentUser.id, currentUser.name);
    });
    
    // 连接错误事件
    socket.on('connect_error', (error) => {
        console.error('连接错误:', error);
        isConnected = false;
    });
    
    // 断开连接事件
    socket.on('disconnect', (reason) => {
        console.log('与Socket.IO服务器断开连接:', reason);
        isConnected = false;
    });
    
    // 用户加入房间事件
    socket.on('user-joined', (data) => {
        console.log(`用户 ${data.userName} 加入了房间，当前用户数: ${data.totalUsers}`);
    });
    
    // 用户离开房间事件
    socket.on('user-left', (data) => {
        console.log(`用户 ${data.userName} 离开了房间，当前用户数: ${data.totalUsers}`);
    });
    
    // ===== 模块一抢答事件 =====
    
    // 抢答结果事件
    socket.on('rush-result', (data) => {
        console.log(`抢答结果: 获胜者 ${data.winnerName}`);
        
        // 更新抢答状态
        isRushMode = true;
        rushWinner = data.winnerId;
        
        // 如果当前用户是抢答成功者
        if (rushWinner === currentUser.id) {
            // 更新状态文本
            const statusText = document.getElementById('status-text');
            if (statusText) {
                statusText.textContent = '抢答成功，请作答';
            }
            
            // 隐藏抢答按钮，显示提交按钮
            const rushBtn = document.getElementById('rush-btn');
            const rushSubmitBtn = document.getElementById('rush-submit-btn');
            
            if (rushBtn && rushSubmitBtn) {
                rushBtn.style.display = 'none';
                rushSubmitBtn.style.display = 'block';
            }
        } else {
            // 当前用户不是抢答成功者
            isWaitingForWinner = true;
            
            // 更新状态文本
            const statusText = document.getElementById('status-text');
            if (statusText) {
                statusText.textContent = `抢答未成功，${data.winnerName} 正在作答`;
            }
            
            // 禁用抢答按钮
            const rushBtn = document.getElementById('rush-btn');
            if (rushBtn) {
                rushBtn.disabled = true;
            }
        }
    });
    
    // 答题完成事件
    socket.on('rush-answer-complete', (data) => {
        console.log(`用户 ${data.userName} 完成了答题，正确: ${data.isCorrect}, 得分: ${data.score}`);
        
        // 如果当前用户不是抢答成功者，则跳过当前题目
        if (rushWinner !== currentUser.id) {
            skipCurrentRushQuestion();
        }
    });
    
    // 抢答状态重置事件
    socket.on('rush-reset', (data) => {
        console.log('抢答状态已重置:', data.message);
        
        // 重置抢答状态
        isRushMode = false;
        rushWinner = null;
        hasRushed = false;
        isWaitingForWinner = false;
        
        // 更新状态文本
        const statusText = document.getElementById('status-text');
        if (statusText) {
            statusText.textContent = '准备抢答（第一个抢答的人才能作答）';
        }
        
        // 启用抢答按钮
        const rushBtn = document.getElementById('rush-btn');
        if (rushBtn) {
            rushBtn.disabled = false;
            rushBtn.style.display = 'block';
        }
        
        // 隐藏提交按钮
        const rushSubmitBtn = document.getElementById('rush-submit-btn');
        if (rushSubmitBtn) {
            rushSubmitBtn.style.display = 'none';
        }
    });
    
    // ===== 模块三抢答事件 =====
    
    // 模块三抢答结果事件
    socket.on('module3-rush-result', (data) => {
        console.log(`模块三抢答结果: 获胜者 ${data.winnerName}`);
        
        // 更新抢答状态
        isModule3RushMode = true;
        module3RushWinner = data.winnerId;
        
        // 如果当前用户是抢答成功者
        if (module3RushWinner === currentUser.id) {
            // 更新状态文本
            const statusText = document.getElementById('status-text');
            if (statusText) {
                statusText.textContent = '抢答成功，请作答';
            }
            
            // 隐藏抢答按钮，显示提交按钮
            const rushBtn = document.getElementById('module3-rush-btn');
            const submitBtn = document.getElementById('module3-submit-btn');
            
            if (rushBtn && submitBtn) {
                rushBtn.style.display = 'none';
                submitBtn.style.display = 'block';
            }
            
            // 播放音频
            const audioElement = document.querySelector('.question-audio');
            if (audioElement) {
                audioElement.play();
            }
        } else {
            // 当前用户不是抢答成功者
            isWaitingForModule3Winner = true;
            
            // 更新状态文本
            const statusText = document.getElementById('status-text');
            if (statusText) {
                statusText.textContent = `抢答未成功，${data.winnerName} 正在作答`;
            }
            
            // 禁用抢答按钮
            const rushBtn = document.getElementById('module3-rush-btn');
            if (rushBtn) {
                rushBtn.disabled = true;
            }
        }
    });
    
    // 模块三答题完成事件
    socket.on('module3-answer-complete', (data) => {
        console.log(`用户 ${data.userName} 完成了模块三答题，正确: ${data.isCorrect}, 得分: ${data.score}`);
        
        // 如果当前用户不是抢答成功者，则跳过当前题目
        if (module3RushWinner !== currentUser.id) {
            // 隐藏结果弹窗
            const resultModal = document.getElementById('module3-result-modal');
            if (resultModal) {
                resultModal.style.display = 'none';
            }
            
            // 增加题目索引
            currentModule3Index++;
            
            // 检查是否已完成所有模块三题目
            if (currentModule3Index >= 3) {
                // 保存当前答题状态
                saveCurrentQuiz();
                
                // 跳转到模块四页面
                window.location.href = 'module4.html';
            } else {
                // 显示下一题
                showModule3Question(module3Questions[currentModule3Index]);
                
                // 更新进度条
                updateModule3Progress();
            }
        }
    });
    
    // 错误事件
    socket.on('error', (data) => {
        console.error('服务器错误:', data.message);
        alert(`错误: ${data.message}`);
    });
}

// 加入房间
function joinRoom(roomId, userId, userName) {
    if (!isConnected || !socket) {
        console.error('未连接到Socket.IO服务器，无法加入房间');
        return;
    }
    
    socket.emit('join-room', {
        roomId: roomId,
        userId: userId,
        userName: userName
    });
}

// ===== 模块一抢答函数 =====

// 尝试抢答
function attemptRush(roomId, userId, userName) {
    if (!isConnected || !socket) {
        console.error('未连接到Socket.IO服务器，无法尝试抢答');
        return false;
    }
    
    socket.emit('rush-attempt', {
        roomId: roomId,
        userId: userId,
        userName: userName
    });
    
    return true;
}

// 提交抢答答案
function submitRushAnswer(roomId, userId, userName, questionId, answer, isCorrect, score) {
    if (!isConnected || !socket) {
        console.error('未连接到Socket.IO服务器，无法提交答案');
        return false;
    }
    
    socket.emit('answer-submitted', {
        roomId: roomId,
        userId: userId,
        userName: userName,
        questionId: questionId,
        answer: answer,
        isCorrect: isCorrect,
        score: score
    });
    
    return true;
}

// 修改原有的handleRush函数，使用Socket.IO
function handleRushWithSocket() {
    // 如果已经有人抢答成功或当前用户已经抢答过，则不允许再次抢答
    if (isRushMode || hasRushed || isWaitingForWinner) {
        return;
    }
    
    // 标记当前用户已经抢答过
    hasRushed = true;
    
    // 尝试通过Socket.IO抢答
    attemptRush(roomId, currentUser.id, currentUser.name);
}

// 修改原有的handleRushSubmit函数，使用Socket.IO
function handleRushSubmitWithSocket() {
    // 如果没有选择答案，提示用户
    if (selectedAnswer === null) {
        alert('请选择一个答案');
        return;
    }
    
    const currentQuestion = rushQuestions[currentRushIndex];
    const isCorrect = selectedAnswer === currentQuestion.correctAnswer;
    
    // 计算得分（抢答题：答对+2分，答错-2分）
    const score = isCorrect ? 2 : -2;
    
    // 记录答案
    currentQuiz.answers.push({
        questionId: currentQuestion.id,
        userAnswer: selectedAnswer,
        isCorrect: isCorrect,
        score: score,
        isRushQuestion: true
    });
    
    // 更新总分
    currentQuiz.totalScore += score;
    
    // 保存当前答题
    saveCurrentQuiz();
    
    // 通过Socket.IO提交答案
    submitRushAnswer(roomId, currentUser.id, currentUser.name, currentQuestion.id, selectedAnswer, isCorrect, score);
    
    // 显示结果
    showRushResult(currentQuestion, isCorrect, score);
}

// ===== 模块三抢答函数 =====

// 尝试模块三抢答
function attemptModule3Rush(roomId, userId, userName) {
    if (!isConnected || !socket) {
        console.error('未连接到Socket.IO服务器，无法尝试模块三抢答');
        return false;
    }
    
    socket.emit('module3-rush-attempt', {
        roomId: roomId,
        userId: userId,
        userName: userName
    });
    
    return true;
}

// 提交模块三抢答答案
function submitModule3Answer(roomId, userId, userName, questionId, answer, isCorrect, score) {
    if (!isConnected || !socket) {
        console.error('未连接到Socket.IO服务器，无法提交模块三答案');
        return false;
    }
    
    socket.emit('module3-answer-submitted', {
        roomId: roomId,
        userId: userId,
        userName: userName,
        questionId: questionId,
        answer: answer,
        isCorrect: isCorrect,
        score: score
    });
    
    return true;
}

// 修改原有的handleModule3Rush函数，使用Socket.IO
function handleModule3RushWithSocket() {
    // 如果已经有人抢答成功或当前用户已经抢答过，则不允许再次抢答
    if (isModule3RushMode || hasModule3Rushed || isWaitingForModule3Winner) {
        return;
    }
    
    // 标记当前用户已经抢答过
    hasModule3Rushed = true;
    
    // 尝试通过Socket.IO抢答
    attemptModule3Rush(roomId, currentUser.id, currentUser.name);
}

// 修改原有的handleModule3Submit函数，使用Socket.IO
function handleModule3SubmitWithSocket() {
    // 如果没有选择答案，提示用户
    if (selectedAnswer === null) {
        alert('请选择一个答案');
        return;
    }
    
    const currentQuestion = module3Questions[currentModule3Index];
    const isCorrect = selectedAnswer === currentQuestion.correctAnswer;
    
    // 计算得分（红歌竞猜：答对+2分，答错-2分）
    const score = isCorrect ? 2 : -2;
    
    // 记录答案
    currentQuiz.answers.push({
        questionId: currentQuestion.id,
        userAnswer: selectedAnswer,
        isCorrect: isCorrect,
        score: score,
        isModule3Question: true
    });
    
    // 更新总分
    currentQuiz.totalScore += score;
    
    // 保存当前答题
    saveCurrentQuiz();
    
    // 通过Socket.IO提交答案
    submitModule3Answer(roomId, currentUser.id, currentUser.name, currentQuestion.id, selectedAnswer, isCorrect, score);
    
    // 显示结果
    showModule3Result(currentQuestion, isCorrect, score);
}

// 页面加载时初始化Socket.IO
document.addEventListener('DOMContentLoaded', function() {
    // 检查当前页面
    const currentPage = window.location.pathname.split('/').pop();
    
    // 只在抢答页面和模块三页面初始化Socket.IO
    if (currentPage === 'rush.html' || currentPage === 'module3.html') {
        // 确保currentUser已加载
        if (currentUser) {
            initSocket();
        } else {
            // 如果currentUser尚未加载，等待加载完成后再初始化
            const checkUserInterval = setInterval(() => {
                if (currentUser) {
                    clearInterval(checkUserInterval);
                    initSocket();
                }
            }, 100);
        }
    }
});
