<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识竞赛</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container" id="login-container">
        <div class="header">
            <div class="logo">
                <img src="https://m.360buyimg.com/i/jfs/t1/320697/40/9482/17412/684f9e07F8473c39f/2cffeb8fce5cac8d.png" alt="上海大学校徽" class="university-logo">
            </div>
            <h1>知识竞赛</h1>
        </div>

        <div class="login-form">
            <h2>基本信息</h2>
            <div class="form-group">
                <label for="username">姓名</label>
                <input type="text" id="username" placeholder="请输入您的姓名" required>
            </div>
            <div class="form-group">
                <label for="birthday">入党生日</label>
                <input type="date" id="birthday" required>
            </div>
            <!-- 主要答题入口：模块八 -->
            <div style="text-align: center; margin: 20px 0;">
                <button id="module8-btn" class="btn btn-primary" style="background-color: #c00000; padding: 15px 40px; font-size: 20px; font-weight: bold; border-radius: 8px; box-shadow: 0 4px 12px rgba(192, 0, 0, 0.3);">🎯 开始党史必答题</button>
                <p style="margin-top: 10px; color: #666; font-size: 14px;">10道党史题目，每题2分，答错不扣分</p>
            </div>

            <div class="module-selection" style="margin-top: 30px; text-align: center;">
                <p style="margin-bottom: 15px; color: #646566; font-weight: bold;">其他答题模块：</p>
                <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                    <!-- 第一行：模块二到模块三 -->
                    <div style="display: flex; width: 100%; gap: 10px; justify-content: center; margin-bottom: 10px;">
                        <button id="module2-btn" class="btn btn-primary" style="flex: 1;">模块二：峥嵘岁月</button>
                        <button id="module3-btn" class="btn btn-primary" style="flex: 1;">模块三：红歌竞猜</button>
                    </div>

                    <!-- 第二行：模块四到模块六 -->
                    <div style="display: flex; width: 100%; gap: 10px; justify-content: center; margin-bottom: 10px;">
                        <button id="module4-btn" class="btn btn-primary" style="flex: 1;">模块四：信念之路</button>
                        <button id="module5-btn" class="btn btn-primary" style="flex: 1;">模块五：分秒必争</button>
                        <button id="module6-btn" class="btn btn-primary" style="flex: 1;">模块六：胜负手</button>
                    </div>
                </div>

                <!-- 已弃用模块提示 -->
                <div style="margin-top: 20px; padding: 10px; background-color: #f8f9fa; border-radius: 4px; border-left: 4px solid #6c757d;">
                    <p style="color: #6c757d; font-size: 12px; margin: 0;">
                        <em>注：模块一（基石考验）和模块七（璀璨星光）已弃用，请使用党史必答题作为主要答题模块</em>
                    </p>
                </div>
            </div>
        </div>

        <div class="rules">
            <h2 style="text-align: center;">答题规则</h2>

            <!-- 主要模块：党史必答题 -->
            <div style="display: flex; justify-content: center; margin-bottom: 20px;">
                <div class="rule-item" style="width: 80%; border: 2px solid #c00000; padding: 20px; border-radius: 8px; background-color: rgba(192, 0, 0, 0.05);">
                    <h3 style="text-align: center; color: #c00000; font-size: 18px;">🎯 党史必答题（模块八）- 主要答题模块</h3>
                    <div style="text-align: center; margin-top: 15px;">
                        <p style="font-size: 16px; margin-bottom: 10px;"><strong>10道中国共产党历史选择题</strong></p>
                        <p style="color: #059669; font-weight: bold;">✅ 答对每题得2分，答错不扣分</p>
                        <p style="color: #666; font-size: 14px; margin-top: 10px;">涵盖从1925年中共四大到2001年加入WTO的重要历史节点</p>
                        <p style="color: #666; font-size: 14px;">包含题目历史背景介绍，寓教于乐</p>
                    </div>
                </div>
            </div>

            <!-- 第一行：模块二和模块三 -->
            <div style="display: flex; gap: 20px; margin-bottom: 20px;">
                <div class="rule-item" style="flex: 1; border: 1px solid #e0e0e0; padding: 15px; border-radius: 5px;">
                    <h3>峥嵘岁月（模块二）</h3>
                    <p>看图猜历史事件和人物</p>
                    <p>答对得1分，答错扣1分</p>
                </div>

                <div class="rule-item" style="flex: 1; border: 1px solid #e0e0e0; padding: 15px; border-radius: 5px;">
                    <h3>红歌竞猜（模块三）</h3>
                    <p>采用集体实时抢答模式</p>
                    <p>抢答成功并答对得2分，抢答成功但答错扣2分</p>
                </div>
            </div>

            <!-- 第二行：模块四和模块五 -->
            <div style="display: flex; gap: 20px; margin-bottom: 20px;">
                <div class="rule-item" style="flex: 1; border: 1px solid #e0e0e0; padding: 15px; border-radius: 5px;">
                    <h3>信念之路（模块四）</h3>
                    <p>控制蛇吃掉汉字方块，拼出红色标语</p>
                    <p>根据完成时间获得1-3分</p>
                </div>

                <div class="rule-item" style="flex: 1; border: 1px solid #e0e0e0; padding: 15px; border-radius: 5px;">
                    <h3>分秒必争（模块五）</h3>
                    <p>高难度、高积分、极速反应的知识填空挑战</p>
                    <p>每题仅有10秒作答时间，难度递增</p>
                    <p>答对前三题每题1分，答对第四题2分，答对第五题3分</p>
                </div>
            </div>

            <!-- 第三行：模块六单独一行居中 -->
            <div style="display: flex; justify-content: center; margin-bottom: 20px;">
                <div class="rule-item" style="width: 60%; border: 1px solid #e0e0e0; padding: 15px; border-radius: 5px;">
                    <h3 style="text-align: center;">胜负手（模块六）</h3>
                    <p>策略风险自选模式，三轮策略博弈</p>
                    <p>三种押注策略：稳健求进（当前总积分的10%）、激流勇进（总分30%）、乾坤一掷（全部积分）</p>
                    <p>先选策略，再答题，根据策略和答题结果计算得分</p>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>© 2025 六月份主题党日知识竞赛活动</p>
        </div>
    </div>

    <script src="questions.js"></script>
    <script src="script.js"></script>
</body>
</html>
