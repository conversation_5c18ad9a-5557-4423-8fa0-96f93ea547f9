<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基石考验 - 高风险抢答</title>
    <link rel="stylesheet" href="styles.css">
    <script src="/socket.io/socket.io.js"></script>
    <script src="socket-client.js"></script>
</head>
<body>
    <div class="container" id="rush-container">
        <div class="header">
            <h1>基石考验 - 高风险抢答</h1>
            <div class="user-info" id="user-info">
                <!-- 用户信息将通过JavaScript动态插入 -->
            </div>
        </div>

        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress" id="rush-progress"></div>
            </div>
            <div class="progress-text">
                <span id="current-rush">1</span>/<span id="total-rush">2</span>
            </div>
        </div>

        <div class="rush-status" id="rush-status">
            <div class="status-text" id="status-text">准备抢答</div>
        </div>

        <div class="question-container" id="rush-question-container">
            <!-- 题目内容将通过JavaScript动态插入 -->
            <div class="question-content" id="rush-question-content"></div>
            
            <div class="options-container" id="rush-options-container">
                <!-- 选项将通过JavaScript动态插入 -->
            </div>
        </div>

        <div class="action-container">
            <button id="rush-btn" class="btn btn-danger">抢答</button>
            <button id="rush-submit-btn" class="btn btn-primary" style="display: none;">提交答案</button>
            <button id="rush-exit-btn" class="btn btn-secondary">退出答题</button>
        </div>

        <!-- 抢答结果弹窗 -->
        <div class="modal" id="rush-result-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>抢答结果</h2>
                </div>
                <div class="modal-body" id="rush-result-content">
                    <!-- 结果内容将通过JavaScript动态插入 -->
                </div>
                <div class="modal-footer">
                    <button id="rush-next-btn" class="btn btn-primary">继续</button>
                </div>
            </div>
        </div>

        <!-- 抢答状态弹窗 -->
        <div class="modal" id="rush-status-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="rush-status-title">抢答状态</h2>
                </div>
                <div class="modal-body" id="rush-status-content">
                    <!-- 状态内容将通过JavaScript动态插入 -->
                </div>
            </div>
        </div>

        <!-- 确认退出弹窗 -->
        <div class="modal" id="rush-exit-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>确认退出</h2>
                </div>
                <div class="modal-body">
                    <p>退出后答题进度将丢失，确定要退出吗？</p>
                </div>
                <div class="modal-footer">
                    <button id="rush-confirm-exit-btn" class="btn btn-danger">确认退出</button>
                    <button id="rush-cancel-exit-btn" class="btn btn-secondary">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script src="questions.js"></script>
    <script src="script.js"></script>
</body>
</html>
