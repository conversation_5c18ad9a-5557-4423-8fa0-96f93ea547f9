# 党史竞赛系统部署修复指南

## 问题分析

经过代码分析，发现以下主要问题：

### 1. JavaScript语法错误
- `script.js` 文件中存在多余的花括号导致语法错误
- 这会阻止页面正常加载和执行

### 2. Socket.IO连接问题
- Socket.IO客户端连接地址不正确
- 事件名称不匹配
- 端口配置问题

### 3. 服务器配置问题
- 后端服务器和Socket.IO服务器端口冲突
- Nginx代理配置需要优化

## 修复步骤

### 第一步：修复JavaScript语法错误

已修复的文件：
- `script.js` - 移除了多余的花括号
- `socket-client.js` - 修正了Socket.IO连接地址
- `rush.html` 和 `module3.html` - 更新了Socket.IO脚本引用

### 第二步：启动服务

1. **给脚本添加执行权限：**
```bash
chmod +x start-services.sh
chmod +x stop-services.sh
```

2. **启动所有服务：**
```bash
./start-services.sh
```

3. **检查服务状态：**
```bash
pm2 list
```

### 第三步：配置Nginx

1. **复制Nginx配置：**
```bash
sudo cp nginx-site.conf /etc/nginx/sites-available/zhishijingsai
sudo ln -s /etc/nginx/sites-available/zhishijingsai /etc/nginx/sites-enabled/
```

2. **测试Nginx配置：**
```bash
sudo nginx -t
```

3. **重启Nginx：**
```bash
sudo systemctl restart nginx
```

### 第四步：验证修复

1. **检查端口占用：**
```bash
netstat -tlnp | grep :80
netstat -tlnp | grep :3000
netstat -tlnp | grep :3001
```

2. **访问测试：**
- 前端页面：http://your-server-ip
- 后端API：http://your-server-ip:3000
- Socket.IO：http://your-server-ip:3001

## 服务架构

```
用户浏览器
    ↓ (HTTP:80)
Nginx反向代理
    ├── 静态文件 → /home/<USER>/
    ├── API请求 → Express服务器 (端口3000)
    └── Socket.IO → Socket.IO服务器 (端口3001)
```

## 常见问题排查

### 1. 页面无法加载
```bash
# 检查Nginx状态
sudo systemctl status nginx

# 检查文件权限
ls -la /home/<USER>/

# 查看Nginx错误日志
sudo tail -f /var/log/nginx/error.log
```

### 2. 抢答功能不工作
```bash
# 检查Socket.IO服务
pm2 logs quiz-socket

# 检查端口3001是否开放
telnet localhost 3001
```

### 3. 后端API不响应
```bash
# 检查后端服务
pm2 logs quiz-backend

# 检查端口3000是否开放
curl http://localhost:3000
```

## 监控和维护

### 查看日志
```bash
# 查看所有PM2服务日志
pm2 logs

# 查看特定服务日志
pm2 logs quiz-backend
pm2 logs quiz-socket

# 查看Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 重启服务
```bash
# 重启特定服务
pm2 restart quiz-backend
pm2 restart quiz-socket

# 重启所有服务
pm2 restart all

# 停止所有服务
./stop-services.sh
```

### 更新代码后
```bash
# 停止服务
./stop-services.sh

# 更新代码
# (上传新文件)

# 重新启动
./start-services.sh
```

## 安全建议

1. **防火墙配置：**
```bash
# 只开放必要端口
sudo ufw allow 80
sudo ufw allow 22
sudo ufw enable
```

2. **文件权限：**
```bash
# 设置适当的文件权限
chmod 644 /home/<USER>/*.html
chmod 644 /home/<USER>/*.js
chmod 644 /home/<USER>/*.css
```

3. **定期备份：**
```bash
# 备份项目文件
tar -czf zhishijingsai-backup-$(date +%Y%m%d).tar.gz /home/<USER>/
```
