#!/bin/bash

# 创建部署包脚本

echo "正在创建部署包..."

# 创建部署目录
mkdir -p deploy-package

# 复制需要更新的文件
echo "复制修改的文件..."

# 主要的修复文件
cp script.js deploy-package/
cp socket-client.js deploy-package/
cp rush.html deploy-package/
cp module3.html deploy-package/

# 后端文件
mkdir -p deploy-package/backend
cp backend/server.js deploy-package/backend/

# Socket服务器文件
mkdir -p deploy-package/socket-server
cp socket-server/server.js deploy-package/socket-server/

# 配置和脚本文件
cp start-services.sh deploy-package/
cp stop-services.sh deploy-package/
cp diagnose.sh deploy-package/
cp nginx-site.conf deploy-package/
cp 部署修复指南.md deploy-package/

# 创建文件列表
echo "创建文件清单..."
cat > deploy-package/文件清单.txt << EOF
部署包文件清单 - $(date)

修复的前端文件：
- script.js (修复JavaScript语法错误)
- socket-client.js (修复Socket.IO连接)
- rush.html (修复Socket.IO引用)
- module3.html (修复Socket.IO引用)

修复的后端文件：
- backend/server.js (修复事件名称)
- socket-server/server.js (Socket.IO服务器)

部署脚本：
- start-services.sh (启动所有服务)
- stop-services.sh (停止所有服务)
- diagnose.sh (系统诊断)

配置文件：
- nginx-site.conf (Nginx配置)

文档：
- 部署修复指南.md (详细部署指南)
EOF

# 创建部署压缩包
echo "创建压缩包..."
tar -czf zhishijingsai-fixed-$(date +%Y%m%d-%H%M%S).tar.gz deploy-package/

echo "部署包创建完成！"
echo "压缩包文件: zhishijingsai-fixed-$(date +%Y%m%d-%H%M%S).tar.gz"
echo ""
echo "请将此压缩包上传到服务器，然后按照部署指南操作。"
