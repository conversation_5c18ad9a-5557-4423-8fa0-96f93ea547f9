部署包文件清单 - <PERSON><PERSON> <PERSON> 24 15:57:10     2025

修复的前端文件：
- script.js (修复JavaScript语法错误)
- socket-client.js (修复Socket.IO连接)
- rush.html (修复Socket.IO引用)
- module3.html (修复Socket.IO引用)

修复的后端文件：
- backend/server.js (修复事件名称)
- socket-server/server.js (Socket.IO服务器)

部署脚本：
- start-services.sh (启动所有服务)
- stop-services.sh (停止所有服务)
- diagnose.sh (系统诊断)

配置文件：
- nginx-site.conf (Nginx配置)

文档：
- 部署修复指南.md (详细部署指南)
