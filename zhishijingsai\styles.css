/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    background-color: #f7f8fa;
    color: #323233;
    line-height: 1.5;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    text-align: center;
    margin-bottom: 20px;
}

.header h1 {
    color: #c00000;
    margin-bottom: 10px;
}

.subtitle {
    color: #646566;
    font-size: 16px;
}

.footer {
    margin-top: auto;
    text-align: center;
    color: #969799;
    font-size: 14px;
    padding: 20px 0;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 4px;
    border: none;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn-primary {
    background-color: #c00000;
    color: white;
}

.btn-primary:hover {
    background-color: #a00000;
}

.btn-secondary {
    background-color: #f2f3f5;
    color: #323233;
}

.btn-secondary:hover {
    background-color: #e2e3e5;
}

.btn-danger {
    background-color: #ee0a24;
    color: white;
}

.btn-danger:hover {
    background-color: #d00a20;
}

/* 登录页样式 */
.logo {
    width: 120px;
    height: 120px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 20px;
}

.university-logo {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.login-form {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.login-form h2 {
    margin-bottom: 20px;
    color: #323233;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #646566;
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #dcdee0;
    border-radius: 4px;
    font-size: 16px;
}

.login-form button {
    width: 100%;
    margin-top: 10px;
}

.rules {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.rules h2 {
    margin-bottom: 15px;
    color: #323233;
}

.rule-item {
    margin-bottom: 15px;
}

.rule-item h3 {
    font-size: 16px;
    color: #323233;
    margin-bottom: 5px;
}

.rule-item p {
    color: #646566;
    font-size: 14px;
}

/* 答题页样式 */
.user-info {
    background-color: #f2f3f5;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 20px;
    text-align: center;
    font-size: 14px;
    color: #646566;
}

.progress-container {
    margin-bottom: 20px;
}

.progress-bar {
    height: 8px;
    background-color: #f2f3f5;
    border-radius: 4px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background-color: #c00000;
    width: 0%;
    transition: width 0.3s;
}

.progress-text {
    text-align: right;
    font-size: 14px;
    color: #646566;
    margin-top: 5px;
}

.timer-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
}

.timer {
    font-size: 24px;
    font-weight: bold;
    color: #c00000;
}

.timer-text {
    font-size: 14px;
    color: #646566;
}

.question-container {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.question-content {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
}

.options-container {
    margin-bottom: 20px;
}

.option-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid #dcdee0;
    border-radius: 4px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.option-item:hover {
    background-color: #f7f8fa;
}

.option-item.selected {
    background-color: #f2f3f5;
    border-color: #c00000;
}

.option-radio {
    margin-right: 10px;
}

.action-container {
    display: flex;
    justify-content: space-between;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.modal-header {
    padding: 15px;
    border-bottom: 1px solid #f2f3f5;
}

.modal-header h2 {
    margin: 0;
    font-size: 18px;
    color: #323233;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px;
    border-top: 1px solid #f2f3f5;
    display: flex;
    justify-content: flex-end;
}

.modal-footer button {
    margin-left: 10px;
}

/* 抢答页样式 */
.rush-status {
    text-align: center;
    margin-bottom: 20px;
}

.status-text {
    font-size: 18px;
    font-weight: bold;
    color: #c00000;
}

/* 结果页样式 */
.score-card {
    background-color: #c00000;
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
}

.score-title {
    font-size: 16px;
    margin-bottom: 10px;
}

.score-value {
    font-size: 48px;
    font-weight: bold;
    margin-bottom: 15px;
}

.score-detail {
    display: flex;
    justify-content: space-around;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 15px;
}

.detail-item {
    display: flex;
    flex-direction: column;
}

.section-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #323233;
}

.answer-list {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.answer-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #f2f3f5;
}

.answer-item:last-child {
    border-bottom: none;
}

.question-type {
    background-color: #2196f3;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    margin-right: 8px;
}

.question-type.rush {
    background-color: #ff9800;
}

.question-type.module2 {
    background-color: #4caf50;
}

.answer-status {
    font-weight: bold;
}

.answer-status.correct {
    color: #07c160;
}

.answer-status.wrong {
    color: #ee0a24;
}

.leaderboard {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.ranking-header {
    display: flex;
    padding: 10px 0;
    border-bottom: 1px solid #f2f3f5;
    font-weight: bold;
}

.ranking-item {
    display: flex;
    padding: 10px 0;
    border-bottom: 1px solid #f2f3f5;
}

.ranking-item:last-child {
    border-bottom: none;
}

.ranking-item.current-user {
    background-color: #f2f3f5;
    border-radius: 4px;
}

.rank-column {
    width: 60px;
    text-align: center;
}

.name-column {
    flex: 1;
}

.score-column {
    width: 60px;
    text-align: right;
    font-weight: bold;
    color: #c00000;
}

.rank-medal {
    display: inline-block;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #ffd700;
    color: white;
    text-align: center;
    line-height: 24px;
    font-weight: bold;
}

.rank-medal.silver {
    background-color: #c0c0c0;
}

.rank-medal.bronze {
    background-color: #cd7f32;
}

/* 模块二样式 */
.image-container {
    margin-bottom: 20px;
    text-align: center;
}

.question-image {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
}

/* 模块八特定样式 */
.question-background {
    background-color: #f8f9fa;
    border-left: 4px solid #c00000;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.question-background-text {
    font-size: 14px;
    color: #666;
    font-style: italic;
    line-height: 1.6;
}

.score-container {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    text-align: center;
}

.score-display {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

.score-label {
    font-size: 16px;
    color: #323233;
}

.score-value {
    font-size: 24px;
    font-weight: bold;
    color: #c00000;
}

.result-info {
    text-align: left;
}

.result-status {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 4px;
    text-align: center;
}

.result-status.correct {
    background-color: #f0f9ff;
    color: #059669;
    border: 1px solid #a7f3d0;
}

.result-status.incorrect {
    background-color: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.result-score {
    font-size: 16px;
    margin-bottom: 15px;
    text-align: center;
}

.score-highlight {
    font-size: 20px;
    font-weight: bold;
    color: #c00000;
}

.result-details {
    margin-bottom: 15px;
}

.result-details p {
    margin-bottom: 8px;
    line-height: 1.5;
}

.result-background {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    border-left: 4px solid #c00000;
}

.result-background p {
    margin-bottom: 8px;
    line-height: 1.6;
}

.complete-info {
    text-align: center;
}

.complete-congratulations {
    font-size: 20px;
    font-weight: bold;
    color: #c00000;
    margin-bottom: 20px;
}

.complete-stats {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #e5e7eb;
}

.stat-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.stat-label {
    font-size: 16px;
    color: #323233;
}

.stat-value {
    font-size: 16px;
    font-weight: bold;
    color: #323233;
}

.stat-value.highlight {
    font-size: 18px;
    color: #c00000;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .container {
        padding: 15px;
    }

    .header h1 {
        font-size: 24px;
    }

    .question-content {
        font-size: 16px;
    }

    .score-value {
        font-size: 36px;
    }

    .action-container {
        flex-direction: column;
    }

    .action-container button {
        margin-bottom: 10px;
        width: 100%;
    }

    .question-image {
        max-height: 200px;
    }

    .score-display {
        flex-direction: column;
        gap: 5px;
    }

    .complete-congratulations {
        font-size: 18px;
    }

    .stat-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}