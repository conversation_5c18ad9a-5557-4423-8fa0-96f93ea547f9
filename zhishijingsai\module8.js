// 模块八：党史必答题
let module8Questions = [];
let currentModule8Index = 0;
let selectedAnswer = null;
let module8Score = 0;

// 党史必答题题目数据
const partyHistoryQuestions = [
    {
        id: 'party_1',
        background: '1925年1月11日：中共四大——确立无产阶级领导权',
        question: '1925年1月召开的中共四大，在中国共产党历史上首次明确提出了哪一个重要问题？',
        options: [
            'A. 武装反抗国民党反动派的总方针',
            'B. 建立革命军队和农村革命根据地',
            'C. 无产阶级在民主革命中的领导权和工农联盟问题',
            'D. 实行土地革命，废除封建土地所有制'
        ],
        correctAnswer: 2, // C选项
        score: 2
    },
    {
        id: 'party_2',
        background: '1925年10月20日：毛泽东发表《发刊词》——以革命立场系统阐释三民主义',
        question: '在1925年10月为国民党广东省党部刊物所作的《发刊词》中，毛泽东将"革命的民生主义"的核心斗争对象定义为什么？',
        options: [
            'A. 帝国主义与国际资本',
            'B. 封建军阀与官僚政府',
            'C. 大商买办阶级与地主阶级',
            'D. 一切剥削阶级与旧思想文化'
        ],
        correctAnswer: 2, // C选项
        score: 2
    },
    {
        id: 'party_3',
        background: '1931年12月14日：宁都起义——壮大红军力量',
        question: '1931年12月14日，国民党第二十六路军发动的宁都起义，其重要历史意义在于什么？',
        options: [
            'A. 促使国民党放弃了对中央苏区的"围剿"',
            'B. 是中国共产党创建人民军队的开端',
            'C. 直接导致了长征的开始',
            'D. 极大地壮大了中央红军的力量，并被改编为红五军团'
        ],
        correctAnswer: 3, // D选项
        score: 2
    },
    {
        id: 'party_4',
        background: '1935年11月28日：《抗日救国宣言》发表',
        question: '1935年11月，红军长征到达陕北后，中共中央发表了《抗日救国宣言》，其主要目的是什么？',
        options: [
            'A. 总结长征的经验教训',
            'B. 提出建立抗日民族统一战线的主张，号召全国共同抗日',
            'C. 宣布建立陕甘宁革命根据地',
            'D. 要求国民党政府立即对日宣战'
        ],
        correctAnswer: 1, // B选项
        score: 2
    },
    {
        id: 'party_5',
        background: '1935年12月17日：瓦窑堡会议召开——锻造抗日民族统一战线',
        question: '1935年12月召开的瓦窑堡会议，其最重要的决定是什么？',
        options: [
            'A. 确定了红军长征的最终目的地',
            'B. 批判了王明的"右"倾投降主义',
            'C. 确立了建立抗日民族统一战线的策略方针',
            'D. 决定在延安建立党的领导核心'
        ],
        correctAnswer: 2, // C选项
        score: 2
    },
    {
        id: 'party_6',
        background: '1941年5月19日：毛泽东作《改造我们的学习》报告——开启延安整风运动',
        question: '1941年毛泽东发表《改造我们的学习》的报告，其核心主张是反对主观主义，提倡什么样的学风？',
        options: [
            'A. 个人服从组织',
            'B. 批评与自我批评',
            'C. 理论联系实际、实事求是',
            'D. 密切联系群众'
        ],
        correctAnswer: 2, // C选项
        score: 2
    },
    {
        id: 'party_7',
        background: '1950年4月19日：关于展开批评和自我批评的决定——制度化党的纪律',
        question: '1950年中共中央决定在报刊上展开批评和自我批评，其主要目的是什么？',
        options: [
            'A. 清算党内的"左"倾和"右"倾错误',
            'B. 为即将开始的土地改革运动进行舆论动员',
            'C. 防止党员干部因执政地位而骄傲自满、脱离群众',
            'D. 鼓励知识分子对国家政策提出不同意见'
        ],
        correctAnswer: 2, // C选项
        score: 2
    },
    {
        id: 'party_8',
        background: '1959年9月26日：大庆油田的发现',
        question: '1959年发现的大庆油田，对于新中国最重要的战略意义是什么？',
        options: [
            'A. 结束了中国贫油的历史，为国家工业化提供了能源保障',
            'B. 首次实现了中美关系的正常化',
            'C. 证明了中国地质理论超越世界水平',
            'D. 促使中国加入了石油输出国组织'
        ],
        correctAnswer: 0, // A选项
        score: 2
    },
    {
        id: 'party_9',
        background: '1984年2月24日：邓小平号召扩大对外开放',
        question: '1984年邓小平在视察经济特区后发表重要谈话，提出了哪一项扩大开放的重大举措？',
        options: [
            'A. 决定在上海浦东进行开发开放',
            'B. 提出加入世界贸易组织',
            'C. 建议开放大连、青岛等一批沿海港口城市',
            'D. 宣布实行"走出去"战略，鼓励对外投资'
        ],
        correctAnswer: 2, // C选项
        score: 2
    },
    {
        id: 'party_10',
        background: '2001年12月11日：中国正式加入世界贸易组织',
        question: '2001年中国正式加入世界贸易组织（WTO），这被视为中国哪一项基本国策的重大成果？',
        options: [
            'A. 独立自主',
            'B. 对外开放',
            'C. 计划生育',
            'D. 一国两制'
        ],
        correctAnswer: 1, // B选项
        score: 2
    }
];

// 初始化模块八页面
function initModule8Page() {
    // 检查是否已登录
    if (!currentUser) {
        window.location.href = 'index.html';
        return;
    }
    
    // 初始化答题数据
    if (!currentQuiz) {
        currentQuiz = {
            userId: currentUser.id,
            startTime: new Date().toISOString(),
            answers: [],
            totalScore: currentUser.totalScore || 0,
            isCompleted: false
        };
        saveCurrentQuiz();
    }
    
    // 显示用户信息
    const userInfo = document.getElementById('user-info');
    if (userInfo) {
        userInfo.textContent = `${currentUser.name} | 入党生日: ${formatDate(currentUser.birthday)}`;
    }
    
    // 初始化题目
    module8Questions = [...partyHistoryQuestions];
    
    // 显示当前积分
    updateScoreDisplay();
    
    // 显示第一题
    showModule8Question(module8Questions[currentModule8Index]);
    
    // 更新进度条
    updateModule8Progress();
    
    // 绑定事件
    bindModule8Events();
}

// 绑定模块八事件
function bindModule8Events() {
    const submitBtn = document.getElementById('module8-submit-btn');
    if (submitBtn) {
        submitBtn.addEventListener('click', handleModule8Submit);
    }
    
    const exitBtn = document.getElementById('module8-exit-btn');
    if (exitBtn) {
        exitBtn.addEventListener('click', showModule8ExitModal);
    }
    
    const nextBtn = document.getElementById('module8-next-btn');
    if (nextBtn) {
        nextBtn.addEventListener('click', handleModule8Next);
    }
    
    const finishBtn = document.getElementById('module8-finish-btn');
    if (finishBtn) {
        finishBtn.addEventListener('click', handleModule8Finish);
    }
    
    const confirmExitBtn = document.getElementById('module8-confirm-exit-btn');
    if (confirmExitBtn) {
        confirmExitBtn.addEventListener('click', () => {
            window.location.href = 'index.html';
        });
    }
    
    const cancelExitBtn = document.getElementById('module8-cancel-exit-btn');
    if (cancelExitBtn) {
        cancelExitBtn.addEventListener('click', hideModule8ExitModal);
    }
}

// 显示模块八题目
function showModule8Question(question) {
    // 显示题目背景
    const backgroundElement = document.getElementById('question-background');
    if (backgroundElement) {
        backgroundElement.innerHTML = `<div class="question-background-text">${question.background}</div>`;
    }
    
    // 显示题目内容
    const questionElement = document.getElementById('module8-question-content');
    if (questionElement) {
        questionElement.textContent = question.question;
    }
    
    // 显示选项
    const optionsContainer = document.getElementById('module8-options-container');
    if (optionsContainer) {
        optionsContainer.innerHTML = '';
        
        question.options.forEach((option, index) => {
            const optionElement = document.createElement('div');
            optionElement.className = 'option-item';
            optionElement.innerHTML = `
                <input type="radio" name="module8-option" value="${index}" class="option-radio" id="module8-option-${index}">
                <label for="module8-option-${index}" class="option-label">${option}</label>
            `;
            
            optionElement.addEventListener('click', () => selectModule8Option(index));
            optionsContainer.appendChild(optionElement);
        });
    }
    
    // 重置选择状态
    selectedAnswer = null;
}

// 选择选项
function selectModule8Option(index) {
    selectedAnswer = index;
    
    // 更新选项样式
    const options = document.querySelectorAll('.option-item');
    options.forEach((option, i) => {
        if (i === index) {
            option.classList.add('selected');
            option.querySelector('input').checked = true;
        } else {
            option.classList.remove('selected');
            option.querySelector('input').checked = false;
        }
    });
}

// 更新进度条
function updateModule8Progress() {
    const progress = document.getElementById('module8-progress');
    const currentQuestion = document.getElementById('current-module8-question');
    const totalQuestions = document.getElementById('total-module8-questions');
    
    if (progress) {
        const percentage = ((currentModule8Index + 1) / module8Questions.length) * 100;
        progress.style.width = `${percentage}%`;
    }
    
    if (currentQuestion) {
        currentQuestion.textContent = currentModule8Index + 1;
    }
    
    if (totalQuestions) {
        totalQuestions.textContent = module8Questions.length;
    }
}

// 更新积分显示
function updateScoreDisplay() {
    const scoreElement = document.getElementById('current-score');
    if (scoreElement) {
        const totalScore = (currentQuiz?.totalScore || 0) + module8Score;
        scoreElement.textContent = totalScore;
    }
}

// 处理提交答案
function handleModule8Submit() {
    // 如果没有选择答案，提示用户
    if (selectedAnswer === null) {
        alert('请选择一个答案');
        return;
    }
    
    const currentQuestion = module8Questions[currentModule8Index];
    const isCorrect = selectedAnswer === currentQuestion.correctAnswer;
    
    // 计算得分（答对+2分，答错不扣分）
    const score = isCorrect ? currentQuestion.score : 0;
    module8Score += score;
    
    // 记录答案
    currentQuiz.answers.push({
        questionId: currentQuestion.id,
        userAnswer: selectedAnswer,
        isCorrect: isCorrect,
        score: score,
        isModule8Question: true
    });
    
    // 更新总分
    currentQuiz.totalScore += score;
    
    // 保存当前答题
    saveCurrentQuiz();
    
    // 更新积分显示
    updateScoreDisplay();
    
    // 显示结果
    showModule8Result(currentQuestion, isCorrect, score);
}

// 显示答题结果
function showModule8Result(question, isCorrect, score) {
    const modal = document.getElementById('module8-result-modal');
    const content = document.getElementById('module8-result-content');
    
    if (content) {
        const correctOption = question.options[question.correctAnswer];
        const userOption = selectedAnswer !== null ? question.options[selectedAnswer] : '未选择';
        
        content.innerHTML = `
            <div class="result-info">
                <div class="result-status ${isCorrect ? 'correct' : 'incorrect'}">
                    ${isCorrect ? '✓ 回答正确' : '✗ 回答错误'}
                </div>
                <div class="result-score">
                    本题得分：<span class="score-highlight">${score}</span> 分
                </div>
                <div class="result-details">
                    <p><strong>正确答案：</strong>${correctOption}</p>
                    <p><strong>您的答案：</strong>${userOption}</p>
                </div>
                <div class="result-background">
                    <p><strong>题目背景：</strong></p>
                    <p>${question.background}</p>
                </div>
            </div>
        `;
    }
    
    // 更新按钮文本
    const nextBtn = document.getElementById('module8-next-btn');
    if (nextBtn) {
        if (currentModule8Index >= module8Questions.length - 1) {
            nextBtn.textContent = '查看结果';
        } else {
            nextBtn.textContent = '下一题';
        }
    }
    
    if (modal) {
        modal.style.display = 'block';
    }
}

// 处理下一题
function handleModule8Next() {
    // 隐藏结果弹窗
    const resultModal = document.getElementById('module8-result-modal');
    if (resultModal) {
        resultModal.style.display = 'none';
    }
    
    // 检查是否还有下一题
    if (currentModule8Index < module8Questions.length - 1) {
        currentModule8Index++;
        showModule8Question(module8Questions[currentModule8Index]);
        updateModule8Progress();
    } else {
        // 所有题目完成，显示完成弹窗
        showModule8Complete();
    }
}

// 显示完成弹窗
function showModule8Complete() {
    // 标记答题完成
    currentQuiz.isCompleted = true;
    currentQuiz.endTime = new Date().toISOString();
    
    // 更新用户总分
    currentUser.totalScore = currentQuiz.totalScore;
    
    // 保存数据
    saveCurrentQuiz();
    saveCurrentUser();
    updateUserInAllUsers();
    
    const modal = document.getElementById('module8-complete-modal');
    const content = document.getElementById('module8-complete-content');
    
    if (content) {
        const correctCount = currentQuiz.answers.filter(a => a.isModule8Question && a.isCorrect).length;
        const totalQuestions = module8Questions.length;
        const accuracy = ((correctCount / totalQuestions) * 100).toFixed(1);
        
        content.innerHTML = `
            <div class="complete-info">
                <div class="complete-congratulations">
                    🎉 恭喜您完成党史必答题！
                </div>
                <div class="complete-stats">
                    <div class="stat-item">
                        <span class="stat-label">答对题数：</span>
                        <span class="stat-value">${correctCount}/${totalQuestions}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">正确率：</span>
                        <span class="stat-value">${accuracy}%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">本次得分：</span>
                        <span class="stat-value highlight">${module8Score} 分</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">总积分：</span>
                        <span class="stat-value highlight">${currentUser.totalScore} 分</span>
                    </div>
                </div>
            </div>
        `;
    }
    
    if (modal) {
        modal.style.display = 'block';
    }
}

// 处理完成答题
function handleModule8Finish() {
    window.location.href = 'leaderboard.html';
}

// 显示退出确认弹窗
function showModule8ExitModal() {
    const modal = document.getElementById('module8-exit-modal');
    if (modal) {
        modal.style.display = 'block';
    }
}

// 隐藏退出确认弹窗
function hideModule8ExitModal() {
    const modal = document.getElementById('module8-exit-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否在模块八页面
    if (document.getElementById('module8-container')) {
        loadUsers();
        initModule8Page();
    }
});
