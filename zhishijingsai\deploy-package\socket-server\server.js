// 引入必要的模块
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const fs = require('fs');
const path = require('path');

// 创建Express应用
const app = express();
const server = http.createServer(app);

// 创建Socket.IO实例
const io = socketIo(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});

// 数据存储路径
const DATA_DIR = path.join(__dirname, 'data');
const ROOMS_FILE = path.join(DATA_DIR, 'rooms.json');

// 确保数据目录存在
if (!fs.existsSync(DATA_DIR)) {
    fs.mkdirSync(DATA_DIR, { recursive: true });
}

// 存储房间状态
let rooms = {
    'default-room': {
        users: {},
        rushWinner: null,
        isRushMode: false,
        currentQuestion: null,
        lastUpdated: Date.now()
    }
};

// 尝试从文件加载房间数据
try {
    if (fs.existsSync(ROOMS_FILE)) {
        const data = fs.readFileSync(ROOMS_FILE, 'utf8');
        const savedRooms = JSON.parse(data);
        rooms = savedRooms;
        console.log('已从文件加载房间数据');
    }
} catch (err) {
    console.error('加载房间数据失败:', err);
}

// 保存房间数据到文件
function saveRoomsToFile() {
    try {
        fs.writeFileSync(ROOMS_FILE, JSON.stringify(rooms), 'utf8');
        console.log('房间数据已保存到文件');
    } catch (err) {
        console.error('保存房间数据失败:', err);
    }
}

// 定期保存房间数据
setInterval(saveRoomsToFile, 10000); // 每10秒保存一次

// 监听Socket.IO连接
io.on('connection', (socket) => {
    console.log('新用户连接:', socket.id);
    
    // 用户加入房间
    socket.on('join-room', (data) => {
        const { roomId, userId, userName } = data;
        
        console.log(`用户 ${userName}(${userId}) 尝试加入房间 ${roomId}`);
        
        // 如果房间不存在，创建房间
        if (!rooms[roomId]) {
            rooms[roomId] = {
                users: {},
                rushWinner: null,
                isRushMode: false,
                currentQuestion: null,
                lastUpdated: Date.now()
            };
        }
        
        // 将用户添加到房间
        rooms[roomId].users[userId] = {
            socketId: socket.id,
            name: userName,
            joinTime: Date.now()
        };
        
        // 将Socket加入房间
        socket.join(roomId);
        
        console.log(`用户 ${userName}(${userId}) 成功加入房间 ${roomId}`);
        
        // 如果房间已经处于抢答模式，通知新用户
        if (rooms[roomId].isRushMode && rooms[roomId].rushWinner) {
            const winnerId = rooms[roomId].rushWinner;
            const winnerName = rooms[roomId].users[winnerId]?.name || '未知用户';
            
            socket.emit('rush-result', {
                winnerId: winnerId,
                winnerName: winnerName
            });
            
            console.log(`通知新用户 ${userName} 抢答已被 ${winnerName} 抢到`);
        }
        
        // 通知房间内所有用户有新用户加入
        io.to(roomId).emit('user-joined', {
            userId: userId,
            userName: userName,
            users: Object.keys(rooms[roomId].users).map(id => ({
                id: id,
                name: rooms[roomId].users[id].name
            }))
        });
        
        // 保存房间状态
        rooms[roomId].lastUpdated = Date.now();
        saveRoomsToFile();
    });
    
    // 用户尝试抢答
    socket.on('rush-attempt', (data) => {
        const { roomId, userId, userName } = data;
        
        console.log(`用户 ${userName}(${userId}) 尝试在房间 ${roomId} 抢答`);
        
        // 检查房间是否存在
        if (!rooms[roomId]) {
            console.log(`房间 ${roomId} 不存在`);
            socket.emit('error', { message: '房间不存在' });
            return;
        }
        
        // 检查房间是否已经处于抢答模式
        if (rooms[roomId].isRushMode) {
            console.log(`房间 ${roomId} 已经处于抢答模式，抢答者: ${rooms[roomId].users[rooms[roomId].rushWinner]?.name}`);
            
            // 通知当前用户抢答失败
            socket.emit('rush-result', {
                winnerId: rooms[roomId].rushWinner,
                winnerName: rooms[roomId].users[rooms[roomId].rushWinner]?.name || '未知用户'
            });
            
            return;
        }
        
        // 设置房间为抢答模式，并记录抢答成功者
        rooms[roomId].isRushMode = true;
        rooms[roomId].rushWinner = userId;
        rooms[roomId].lastUpdated = Date.now();
        
        console.log(`用户 ${userName}(${userId}) 在房间 ${roomId} 抢答成功`);
        
        // 通知房间内所有用户抢答结果
        io.to(roomId).emit('rush-result', {
            winnerId: userId,
            winnerName: userName
        });
        
        // 保存房间状态
        saveRoomsToFile();
    });
    
    // 用户提交抢答答案
    socket.on('rush-answer-submitted', (data) => {
        const { roomId, userId, userName, questionId, answer, isCorrect, score } = data;
        
        console.log(`用户 ${userName}(${userId}) 在房间 ${roomId} 提交了答案`);
        
        // 检查房间是否存在
        if (!rooms[roomId]) {
            console.log(`房间 ${roomId} 不存在`);
            socket.emit('error', { message: '房间不存在' });
            return;
        }
        
        // 检查用户是否是抢答成功者
        if (rooms[roomId].rushWinner !== userId) {
            console.log(`用户 ${userName}(${userId}) 不是抢答成功者`);
            socket.emit('error', { message: '您不是抢答成功者' });
            return;
        }
        
        console.log(`用户 ${userName}(${userId}) 在房间 ${roomId} 提交了答案: ${answer}, 正确: ${isCorrect}, 得分: ${score}`);
        
        // 重置房间状态
        rooms[roomId].isRushMode = false;
        rooms[roomId].rushWinner = null;
        rooms[roomId].lastUpdated = Date.now();
        
        // 通知房间内所有用户答题完成
        io.to(roomId).emit('rush-answer-complete', {
            userId: userId,
            userName: userName,
            questionId: questionId,
            isCorrect: isCorrect,
            score: score
        });
        
        // 保存房间状态
        saveRoomsToFile();
    });
    
    // 用户断开连接
    socket.on('disconnect', () => {
        console.log('用户断开连接:', socket.id);
        
        // 从所有房间中移除用户
        for (const roomId in rooms) {
            const room = rooms[roomId];
            
            for (const userId in room.users) {
                if (room.users[userId].socketId === socket.id) {
                    const userName = room.users[userId].name;
                    console.log(`用户 ${userName}(${userId}) 离开房间 ${roomId}`);
                    
                    // 如果离开的用户是抢答成功者，重置房间状态
                    if (room.rushWinner === userId) {
                        room.isRushMode = false;
                        room.rushWinner = null;
                        room.lastUpdated = Date.now();
                        
                        // 通知房间内所有用户抢答重置
                        io.to(roomId).emit('rush-reset', {
                            reason: '抢答成功者离开了房间'
                        });
                    }
                    
                    // 从房间中移除用户
                    delete room.users[userId];
                    
                    // 通知房间内所有用户有用户离开
                    io.to(roomId).emit('user-left', {
                        userId: userId,
                        userName: userName,
                        users: Object.keys(room.users).map(id => ({
                            id: id,
                            name: room.users[id].name
                        }))
                    });
                    
                    // 保存房间状态
                    saveRoomsToFile();
                    break;
                }
            }
        }
    });
// 用户尝试模块三抢答
socket.on('module3-rush-attempt', (data) => {
    const { roomId, userId, userName } = data;
    
    console.log(`用户 ${userName}(${userId}) 尝试在房间 ${roomId} 模块三抢答`);
    
    // 检查房间是否存在
    if (!rooms[roomId]) {
        // 如果房间不存在，创建房间
        rooms[roomId] = {
            users: {},
            rushWinner: null,
            isRushMode: false,
            currentQuestion: null,
            lastUpdated: Date.now()
        };
    }
    
    // 检查房间是否已经处于抢答模式
    if (rooms[roomId].isRushMode) {
        console.log(`房间 ${roomId} 已经处于抢答模式，抢答者: ${rooms[roomId].users[rooms[roomId].rushWinner]?.name}`);
        
        // 通知当前用户抢答失败
        socket.emit('module3-rush-result', {
            winnerId: rooms[roomId].rushWinner,
            winnerName: rooms[roomId].users[rooms[roomId].rushWinner]?.name || '未知用户'
        });
        
        return;
    }
    
    // 将用户添加到房间（如果尚未添加）
    if (!rooms[roomId].users[userId]) {
        rooms[roomId].users[userId] = {
            socketId: socket.id,
            name: userName,
            joinTime: Date.now()
        };
        
        // 将Socket加入房间
        socket.join(roomId);
    }
    
    // 设置房间为抢答模式，并记录抢答成功者
    rooms[roomId].isRushMode = true;
    rooms[roomId].rushWinner = userId;
    rooms[roomId].lastUpdated = Date.now();
    
    console.log(`用户 ${userName}(${userId}) 在房间 ${roomId} 模块三抢答成功`);
    
    // 通知房间内所有用户抢答结果
    io.to(roomId).emit('module3-rush-result', {
        winnerId: userId,
        winnerName: userName
    });
    
    // 保存房间状态
    saveRoomsToFile();
});

// 用户提交模块三抢答答案
socket.on('module3-answer-submitted', (data) => {
    const { roomId, userId, userName, questionId, answer, isCorrect, score } = data;
    
    console.log(`用户 ${userName}(${userId}) 在房间 ${roomId} 提交了模块三答案`);
    
    // 检查房间是否存在
    if (!rooms[roomId]) {
        console.log(`房间 ${roomId} 不存在`);
        socket.emit('error', { message: '房间不存在' });
        return;
    }
    
    // 检查用户是否是抢答成功者
    if (rooms[roomId].rushWinner !== userId) {
        console.log(`用户 ${userName}(${userId}) 不是抢答成功者`);
        socket.emit('error', { message: '您不是抢答成功者' });
        return;
    }
    
    console.log(`用户 ${userName}(${userId}) 在房间 ${roomId} 提交了模块三答案: ${answer}, 正确: ${isCorrect}, 得分: ${score}`);
    
    // 重置房间状态
    rooms[roomId].isRushMode = false;
    rooms[roomId].rushWinner = null;
    rooms[roomId].lastUpdated = Date.now();
    
    // 通知房间内所有用户答题完成
    io.to(roomId).emit('module3-answer-complete', {
        userId: userId,
        userName: userName,
        questionId: questionId,
        isCorrect: isCorrect,
        score: score
    });
    
    // 保存房间状态
    saveRoomsToFile();
});
    
    // 心跳检测
    socket.on('heartbeat', (data) => {
        const { roomId, userId } = data;
        
        if (rooms[roomId] && rooms[roomId].users[userId]) {
            rooms[roomId].users[userId].lastHeartbeat = Date.now();
        }
    });
});

// 清理不活跃的用户
setInterval(() => {
    const now = Date.now();
    
    for (const roomId in rooms) {
        const room = rooms[roomId];
        
        for (const userId in room.users) {
            const user = room.users[userId];
            
            // 如果用户超过5分钟没有心跳，从房间中移除
            if (user.lastHeartbeat && now - user.lastHeartbeat > 5 * 60 * 1000) {
                console.log(`用户 ${user.name}(${userId}) 因不活跃被移除`);
                
                // 如果离开的用户是抢答成功者，重置房间状态
                if (room.rushWinner === userId) {
                    room.isRushMode = false;
                    room.rushWinner = null;
                    room.lastUpdated = Date.now();
                    
                    // 通知房间内所有用户抢答重置
                    io.to(roomId).emit('rush-reset', {
                        reason: '抢答成功者不活跃'
                    });
                }
                
                // 从房间中移除用户
                delete room.users[userId];
                
                // 通知房间内所有用户有用户离开
                io.to(roomId).emit('user-left', {
                    userId: userId,
                    userName: user.name,
                    users: Object.keys(room.users).map(id => ({
                        id: id,
                        name: room.users[id].name
                    }))
                });
            }
        }
    }
    
    // 保存房间状态
    saveRoomsToFile();
}, 60 * 1000); // 每分钟检查一次

// 启动服务器
const PORT = process.env.PORT || 3001; // 使用3001端口避免冲突
server.listen(PORT, () => {
    console.log(`Socket.IO服务器运行在端口 ${PORT}`);
});
