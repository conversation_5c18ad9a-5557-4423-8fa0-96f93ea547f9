#!/bin/bash

# 党史竞赛系统启动脚本

echo "正在启动党史竞赛系统..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "错误: Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 检查PM2是否安装
if ! command -v pm2 &> /dev/null; then
    echo "正在安装 PM2..."
    npm install -g pm2
fi

# 进入项目目录
cd "$(dirname "$0")"

echo "当前目录: $(pwd)"

# 启动后端服务器 (端口 3000)
echo "正在启动后端服务器..."
cd backend
if [ ! -d "node_modules" ]; then
    echo "正在安装后端依赖..."
    npm install
fi

# 使用PM2启动后端服务
pm2 start server.js --name "quiz-backend" --watch
cd ..

# 启动Socket.IO服务器 (端口 3001)
echo "正在启动Socket.IO服务器..."
cd socket-server
if [ ! -d "node_modules" ]; then
    echo "正在安装Socket.IO服务器依赖..."
    npm install
fi

# 使用PM2启动Socket.IO服务
pm2 start server.js --name "quiz-socket" --watch
cd ..

# 检查Nginx是否运行
if ! pgrep nginx > /dev/null; then
    echo "正在启动Nginx..."
    sudo systemctl start nginx
fi

echo "所有服务已启动完成！"
echo ""
echo "服务状态:"
pm2 list
echo ""
echo "访问地址:"
echo "前端页面: http://localhost"
echo "后端API: http://localhost:3000"
echo "Socket.IO: http://localhost:3001"
echo ""
echo "查看日志:"
echo "pm2 logs quiz-backend"
echo "pm2 logs quiz-socket"
