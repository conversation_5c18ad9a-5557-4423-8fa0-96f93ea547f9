#!/bin/bash

# 服务器端部署脚本
# 在华为云Ubuntu服务器上运行此脚本

echo "=== 党史竞赛系统服务器部署脚本 ==="
echo "开始时间: $(date)"
echo ""

# 检查是否为root用户或有sudo权限
if [[ $EUID -eq 0 ]]; then
   echo "检测到root用户权限"
elif sudo -n true 2>/dev/null; then
   echo "检测到sudo权限"
else
   echo "❌ 需要sudo权限来执行此脚本"
   exit 1
fi

# 设置项目路径
PROJECT_PATH="/home/<USER>"
BACKUP_PATH="/home/<USER>"

echo "项目路径: $PROJECT_PATH"
echo "备份路径: $BACKUP_PATH"
echo ""

# 第一步：停止现有服务
echo "1. 停止现有服务..."
pm2 stop all 2>/dev/null || echo "没有运行的PM2服务"
pm2 delete all 2>/dev/null || echo "没有PM2进程需要删除"

# 停止可能占用端口的进程
echo "检查并停止占用端口的进程..."
sudo fuser -k 3000/tcp 2>/dev/null || echo "端口3000未被占用"
sudo fuser -k 3001/tcp 2>/dev/null || echo "端口3001未被占用"

# 第二步：备份现有项目
echo ""
echo "2. 备份现有项目..."
if [ -d "$PROJECT_PATH" ]; then
    sudo cp -r "$PROJECT_PATH" "$BACKUP_PATH"
    echo "✅ 项目已备份到: $BACKUP_PATH"
else
    echo "⚠️  项目目录不存在，将创建新目录"
    sudo mkdir -p "$PROJECT_PATH"
fi

# 第三步：检查部署包
echo ""
echo "3. 检查部署包..."
DEPLOY_FILE=$(ls zhishijingsai-fixed-*.tar.gz 2>/dev/null | head -1)
if [ -z "$DEPLOY_FILE" ]; then
    echo "❌ 未找到部署包文件 (zhishijingsai-fixed-*.tar.gz)"
    echo "请确保已上传部署包到当前目录"
    exit 1
fi

echo "找到部署包: $DEPLOY_FILE"

# 第四步：解压并部署
echo ""
echo "4. 解压部署包..."
tar -xzf "$DEPLOY_FILE"
if [ ! -d "deploy-package" ]; then
    echo "❌ 部署包解压失败"
    exit 1
fi

echo "✅ 部署包解压成功"

# 第五步：复制文件到项目目录
echo ""
echo "5. 部署文件到项目目录..."

# 复制前端文件
sudo cp deploy-package/script.js "$PROJECT_PATH/"
sudo cp deploy-package/socket-client.js "$PROJECT_PATH/"
sudo cp deploy-package/rush.html "$PROJECT_PATH/"
sudo cp deploy-package/module3.html "$PROJECT_PATH/"

# 复制后端文件
sudo cp deploy-package/backend/server.js "$PROJECT_PATH/backend/"
sudo cp deploy-package/socket-server/server.js "$PROJECT_PATH/socket-server/"

# 复制脚本文件
sudo cp deploy-package/start-services.sh "$PROJECT_PATH/"
sudo cp deploy-package/stop-services.sh "$PROJECT_PATH/"
sudo cp deploy-package/diagnose.sh "$PROJECT_PATH/"

# 复制配置文件
sudo cp deploy-package/nginx-site.conf "$PROJECT_PATH/"
sudo cp deploy-package/部署修复指南.md "$PROJECT_PATH/"

echo "✅ 文件部署完成"

# 第六步：设置文件权限
echo ""
echo "6. 设置文件权限..."
sudo chown -R www-data:www-data "$PROJECT_PATH"
sudo chmod +x "$PROJECT_PATH"/*.sh
sudo chmod 644 "$PROJECT_PATH"/*.html
sudo chmod 644 "$PROJECT_PATH"/*.js
sudo chmod 644 "$PROJECT_PATH"/*.css

echo "✅ 文件权限设置完成"

# 第七步：安装依赖
echo ""
echo "7. 安装Node.js依赖..."

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "安装Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# 检查PM2
if ! command -v pm2 &> /dev/null; then
    echo "安装PM2..."
    sudo npm install -g pm2
fi

# 安装后端依赖
echo "安装后端依赖..."
cd "$PROJECT_PATH/backend"
sudo npm install

# 安装Socket服务器依赖
echo "安装Socket服务器依赖..."
cd "$PROJECT_PATH/socket-server"
sudo npm install

cd "$PROJECT_PATH"
echo "✅ 依赖安装完成"

# 第八步：配置Nginx
echo ""
echo "8. 配置Nginx..."
if [ -f "$PROJECT_PATH/nginx-site.conf" ]; then
    sudo cp "$PROJECT_PATH/nginx-site.conf" /etc/nginx/sites-available/zhishijingsai
    sudo ln -sf /etc/nginx/sites-available/zhishijingsai /etc/nginx/sites-enabled/
    
    # 删除默认站点（如果存在）
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # 测试Nginx配置
    if sudo nginx -t; then
        echo "✅ Nginx配置测试通过"
        sudo systemctl restart nginx
        echo "✅ Nginx已重启"
    else
        echo "❌ Nginx配置测试失败"
        exit 1
    fi
else
    echo "⚠️  Nginx配置文件不存在，跳过配置"
fi

# 第九步：启动服务
echo ""
echo "9. 启动所有服务..."
cd "$PROJECT_PATH"
chmod +x start-services.sh
./start-services.sh

# 第十步：验证部署
echo ""
echo "10. 验证部署..."
sleep 5

echo "检查服务状态:"
pm2 list

echo ""
echo "检查端口占用:"
netstat -tlnp | grep :80 && echo "✅ 端口80正常" || echo "❌ 端口80异常"
netstat -tlnp | grep :3000 && echo "✅ 端口3000正常" || echo "❌ 端口3000异常"
netstat -tlnp | grep :3001 && echo "✅ 端口3001正常" || echo "❌ 端口3001异常"

echo ""
echo "=== 部署完成 ==="
echo "完成时间: $(date)"
echo ""
echo "访问地址:"
echo "- 主页: http://$(curl -s ifconfig.me || hostname -I | awk '{print $1}')"
echo "- 本地测试: http://localhost"
echo ""
echo "如果遇到问题，请运行诊断脚本:"
echo "cd $PROJECT_PATH && ./diagnose.sh"
echo ""
echo "查看日志:"
echo "pm2 logs"
