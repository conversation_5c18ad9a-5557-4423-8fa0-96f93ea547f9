<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模块八测试页面</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .test-container {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        .test-button {
            display: block;
            width: 100%;
            margin: 10px 0;
            padding: 15px;
            background-color: #c00000;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #a00000;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
            border-left: 4px solid #c00000;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>模块八功能测试</h1>
        </div>

        <div class="test-container">
            <h2>测试步骤</h2>
            
            <button class="test-button" onclick="testLogin()">1. 测试登录功能</button>
            <div id="login-result" class="test-result" style="display: none;"></div>
            
            <button class="test-button" onclick="testModule8Access()">2. 测试模块八访问</button>
            <div id="access-result" class="test-result" style="display: none;"></div>
            
            <button class="test-button" onclick="testQuestionData()">3. 测试题目数据</button>
            <div id="question-result" class="test-result" style="display: none;"></div>
            
            <button class="test-button" onclick="testScoring()">4. 测试积分系统</button>
            <div id="scoring-result" class="test-result" style="display: none;"></div>
            
            <button class="test-button" onclick="openModule8()">5. 打开模块八页面</button>
            
            <div style="margin-top: 20px; padding: 15px; background-color: #e8f5e8; border-radius: 4px;">
                <h3>测试说明：</h3>
                <ul>
                    <li>点击按钮1-4进行功能测试</li>
                    <li>点击按钮5直接打开模块八页面进行实际测试</li>
                    <li>确保所有测试都通过后，模块八就可以正常使用了</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="questions.js"></script>
    <script src="script.js"></script>
    <script src="module8.js"></script>
    
    <script>
        function testLogin() {
            const result = document.getElementById('login-result');
            result.style.display = 'block';
            
            try {
                // 创建测试用户
                const testUser = {
                    id: 'test_' + Date.now(),
                    name: '测试用户',
                    birthday: '2020-01-01',
                    totalScore: 0
                };
                
                // 保存到localStorage
                localStorage.setItem('currentUser', JSON.stringify(testUser));
                
                // 验证保存
                const savedUser = JSON.parse(localStorage.getItem('currentUser'));
                if (savedUser && savedUser.name === '测试用户') {
                    result.innerHTML = '✅ 登录功能测试通过：用户数据保存成功';
                    result.style.borderLeftColor = '#059669';
                } else {
                    result.innerHTML = '❌ 登录功能测试失败：用户数据保存失败';
                    result.style.borderLeftColor = '#dc2626';
                }
            } catch (error) {
                result.innerHTML = '❌ 登录功能测试失败：' + error.message;
                result.style.borderLeftColor = '#dc2626';
            }
        }
        
        function testModule8Access() {
            const result = document.getElementById('access-result');
            result.style.display = 'block';
            
            try {
                // 检查module8.html是否存在
                fetch('module8.html')
                    .then(response => {
                        if (response.ok) {
                            result.innerHTML = '✅ 模块八页面访问测试通过：module8.html文件存在';
                            result.style.borderLeftColor = '#059669';
                        } else {
                            result.innerHTML = '❌ 模块八页面访问测试失败：module8.html文件不存在';
                            result.style.borderLeftColor = '#dc2626';
                        }
                    })
                    .catch(error => {
                        result.innerHTML = '❌ 模块八页面访问测试失败：' + error.message;
                        result.style.borderLeftColor = '#dc2626';
                    });
            } catch (error) {
                result.innerHTML = '❌ 模块八页面访问测试失败：' + error.message;
                result.style.borderLeftColor = '#dc2626';
            }
        }
        
        function testQuestionData() {
            const result = document.getElementById('question-result');
            result.style.display = 'block';
            
            try {
                // 检查题目数据
                if (typeof partyHistoryQuestions !== 'undefined' && partyHistoryQuestions.length === 10) {
                    let allValid = true;
                    let details = [];
                    
                    partyHistoryQuestions.forEach((q, index) => {
                        if (!q.id || !q.question || !q.options || q.options.length !== 4 || typeof q.correctAnswer !== 'number' || q.score !== 2) {
                            allValid = false;
                            details.push(`题目${index + 1}数据不完整`);
                        }
                    });
                    
                    if (allValid) {
                        result.innerHTML = '✅ 题目数据测试通过：10道题目数据完整，每题2分';
                        result.style.borderLeftColor = '#059669';
                    } else {
                        result.innerHTML = '❌ 题目数据测试失败：' + details.join(', ');
                        result.style.borderLeftColor = '#dc2626';
                    }
                } else {
                    result.innerHTML = '❌ 题目数据测试失败：题目数量不正确或数据未加载';
                    result.style.borderLeftColor = '#dc2626';
                }
            } catch (error) {
                result.innerHTML = '❌ 题目数据测试失败：' + error.message;
                result.style.borderLeftColor = '#dc2626';
            }
        }
        
        function testScoring() {
            const result = document.getElementById('scoring-result');
            result.style.display = 'block';
            
            try {
                // 模拟答题和计分
                const testUser = JSON.parse(localStorage.getItem('currentUser'));
                if (!testUser) {
                    result.innerHTML = '❌ 积分系统测试失败：请先运行登录测试';
                    result.style.borderLeftColor = '#dc2626';
                    return;
                }
                
                // 创建测试答题数据
                const testQuiz = {
                    userId: testUser.id,
                    startTime: new Date().toISOString(),
                    answers: [
                        { questionId: 'party_1', isCorrect: true, score: 2, isModule8Question: true },
                        { questionId: 'party_2', isCorrect: false, score: 0, isModule8Question: true },
                        { questionId: 'party_3', isCorrect: true, score: 2, isModule8Question: true }
                    ],
                    totalScore: 4,
                    isCompleted: false
                };
                
                localStorage.setItem('currentQuiz', JSON.stringify(testQuiz));
                
                // 验证积分计算
                const savedQuiz = JSON.parse(localStorage.getItem('currentQuiz'));
                if (savedQuiz && savedQuiz.totalScore === 4) {
                    result.innerHTML = '✅ 积分系统测试通过：答对得2分，答错不扣分，积分计算正确';
                    result.style.borderLeftColor = '#059669';
                } else {
                    result.innerHTML = '❌ 积分系统测试失败：积分计算错误';
                    result.style.borderLeftColor = '#dc2626';
                }
            } catch (error) {
                result.innerHTML = '❌ 积分系统测试失败：' + error.message;
                result.style.borderLeftColor = '#dc2626';
            }
        }
        
        function openModule8() {
            // 确保有测试用户
            const testUser = JSON.parse(localStorage.getItem('currentUser'));
            if (!testUser) {
                alert('请先运行登录测试创建测试用户');
                return;
            }
            
            // 打开模块八页面
            window.open('module8.html', '_blank');
        }
    </script>
</body>
</html>
