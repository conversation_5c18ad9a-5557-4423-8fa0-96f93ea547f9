#!/bin/bash

# 紧急修复脚本 - 修复开始答题按钮问题

echo "=== 紧急修复：开始答题按钮问题 ==="

PROJECT_PATH="/home/<USER>"

# 1. 检查当前问题
echo "1. 检查当前状态..."
echo "检查script.js文件："
if [ -f "$PROJECT_PATH/script.js" ]; then
    echo "✅ script.js 文件存在"
    echo "文件大小: $(wc -c < $PROJECT_PATH/script.js) 字节"
    echo "修改时间: $(stat -c %y $PROJECT_PATH/script.js)"
else
    echo "❌ script.js 文件不存在"
fi

# 2. 检查JavaScript语法
echo ""
echo "2. 检查JavaScript语法..."
if command -v node &> /dev/null; then
    if node -c "$PROJECT_PATH/script.js" 2>/dev/null; then
        echo "✅ script.js 语法正确"
    else
        echo "❌ script.js 语法错误："
        node -c "$PROJECT_PATH/script.js"
    fi
else
    echo "⚠️  Node.js 未安装，无法检查语法"
fi

# 3. 检查handleLogin函数
echo ""
echo "3. 检查handleLogin函数..."
if grep -q "function handleLogin" "$PROJECT_PATH/script.js"; then
    echo "✅ handleLogin函数存在"
    echo "函数位置："
    grep -n "function handleLogin" "$PROJECT_PATH/script.js"
else
    echo "❌ handleLogin函数不存在"
fi

# 4. 检查index.html中的事件绑定
echo ""
echo "4. 检查index.html中的按钮事件..."
if [ -f "$PROJECT_PATH/index.html" ]; then
    if grep -q "开始答题" "$PROJECT_PATH/index.html"; then
        echo "✅ 找到开始答题按钮"
        echo "按钮代码："
        grep -A2 -B2 "开始答题" "$PROJECT_PATH/index.html"
    else
        echo "❌ 未找到开始答题按钮"
    fi
else
    echo "❌ index.html 文件不存在"
fi

# 5. 创建简化的测试版本
echo ""
echo "5. 创建测试版本..."

# 备份当前文件
cp "$PROJECT_PATH/index.html" "$PROJECT_PATH/index.html.backup" 2>/dev/null

# 创建简化的测试按钮
cat > "$PROJECT_PATH/test-button.html" << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>测试按钮</title>
</head>
<body>
    <h1>测试开始答题按钮</h1>
    <button onclick="testFunction()">测试按钮</button>
    <button onclick="handleLogin(null, 'quiz.html')">开始答题</button>
    
    <script>
        function testFunction() {
            alert('测试按钮工作正常！');
        }
        
        function handleLogin(event, targetPage) {
            console.log('handleLogin被调用，目标页面:', targetPage);
            alert('handleLogin函数被调用，即将跳转到: ' + targetPage);
            
            // 简单跳转测试
            if (targetPage) {
                window.location.href = targetPage;
            }
        }
    </script>
</body>
</html>
EOF

echo "✅ 创建了测试页面: $PROJECT_PATH/test-button.html"

# 6. 检查文件权限
echo ""
echo "6. 修复文件权限..."
sudo chown www-data:www-data "$PROJECT_PATH"/*.html "$PROJECT_PATH"/*.js 2>/dev/null
sudo chmod 644 "$PROJECT_PATH"/*.html "$PROJECT_PATH"/*.js 2>/dev/null
echo "✅ 文件权限已修复"

# 7. 重启Web服务
echo ""
echo "7. 重启Web服务..."
sudo systemctl restart nginx
echo "✅ Nginx已重启"

echo ""
echo "=== 修复完成 ==="
echo ""
echo "请测试以下页面："
echo "1. 原始页面: http://your-server-ip/"
echo "2. 测试页面: http://your-server-ip/test-button.html"
echo ""
echo "如果测试页面的按钮工作正常，说明问题在原始页面的JavaScript代码中。"
echo "请在浏览器中按F12查看控制台错误信息。"
