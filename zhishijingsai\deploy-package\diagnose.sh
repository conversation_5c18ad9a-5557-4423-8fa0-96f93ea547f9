#!/bin/bash

# 党史竞赛系统诊断脚本

echo "=== 党史竞赛系统诊断报告 ==="
echo "时间: $(date)"
echo ""

# 检查Node.js
echo "1. Node.js 版本:"
if command -v node &> /dev/null; then
    node --version
else
    echo "❌ Node.js 未安装"
fi
echo ""

# 检查PM2
echo "2. PM2 状态:"
if command -v pm2 &> /dev/null; then
    pm2 list
else
    echo "❌ PM2 未安装"
fi
echo ""

# 检查端口占用
echo "3. 端口占用情况:"
echo "端口 80 (Nginx):"
netstat -tlnp | grep :80 || echo "端口80未被占用"

echo "端口 3000 (后端API):"
netstat -tlnp | grep :3000 || echo "端口3000未被占用"

echo "端口 3001 (Socket.IO):"
netstat -tlnp | grep :3001 || echo "端口3001未被占用"
echo ""

# 检查Nginx状态
echo "4. Nginx 状态:"
if command -v nginx &> /dev/null; then
    sudo systemctl status nginx --no-pager -l
else
    echo "❌ Nginx 未安装"
fi
echo ""

# 检查文件权限
echo "5. 项目文件权限:"
ls -la /home/<USER>/ | head -10
echo ""

# 检查JavaScript语法
echo "6. JavaScript 语法检查:"
if command -v node &> /dev/null; then
    echo "检查 script.js:"
    node -c /home/<USER>/script.js && echo "✅ script.js 语法正确" || echo "❌ script.js 语法错误"
    
    echo "检查 socket-client.js:"
    node -c /home/<USER>/socket-client.js && echo "✅ socket-client.js 语法正确" || echo "❌ socket-client.js 语法错误"
    
    echo "检查 questions.js:"
    node -c /home/<USER>/questions.js && echo "✅ questions.js 语法正确" || echo "❌ questions.js 语法错误"
fi
echo ""

# 测试HTTP连接
echo "7. HTTP 连接测试:"
echo "测试主页:"
curl -s -o /dev/null -w "HTTP状态码: %{http_code}\n" http://localhost/ || echo "❌ 无法连接到主页"

echo "测试后端API:"
curl -s -o /dev/null -w "HTTP状态码: %{http_code}\n" http://localhost:3000/ || echo "❌ 无法连接到后端API"

echo "测试Socket.IO:"
curl -s -o /dev/null -w "HTTP状态码: %{http_code}\n" http://localhost:3001/socket.io/ || echo "❌ 无法连接到Socket.IO"
echo ""

# 检查日志
echo "8. 最近的错误日志:"
echo "Nginx 错误日志 (最后10行):"
sudo tail -10 /var/log/nginx/error.log 2>/dev/null || echo "无法读取Nginx错误日志"
echo ""

echo "PM2 日志:"
pm2 logs --lines 5 2>/dev/null || echo "无PM2日志"
echo ""

echo "=== 诊断完成 ==="
echo ""
echo "如果发现问题，请参考 部署修复指南.md 进行修复"
