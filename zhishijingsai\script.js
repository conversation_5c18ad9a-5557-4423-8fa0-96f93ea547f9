
// 全局变量
let currentUser = null;
let currentQuiz = null;
let currentQuestionIndex = 0;
let selectedAnswer = null;
let timer = null;
let timerValue = 30;
let rushQuestions = [];
let currentRushIndex = 0;
let isRushMode = false;
let rushWinner = null;
let allUsers = [];
let hasRushed = false; // 标记当前用户是否已经抢答过
let isWaitingForWinner = false; // 标记是否正在等待抢答获胜者完成答题
let module2Questions = []; // 模块二题目
let currentModule2Index = 0; // 当前模块二题目索引
let module3Questions = []; // 模块三题目
let currentModule3Index = 0; // 当前模块三题目索引
let module3RushWinner = null; // 模块三抢答成功者
let hasModule3Rushed = false; // 标记当前用户是否已经在模块三抢答过
let isModule3RushMode = false; // 标记是否处于模块三抢答模式
let isWaitingForModule3Winner = false; // 标记是否正在等待模块三抢答获胜者完成答题

// 页面加载时执行
document.addEventListener('DOMContentLoaded', function() {
    // 从localStorage加载用户数据
    loadUsers();
    
    // 根据当前页面执行不同的初始化
    const currentPage = window.location.pathname.split('/').pop();
    
    if (currentPage === 'index.html' || currentPage === '') {
        initLoginPage();
    } else if (currentPage === 'quiz.html') {
        initQuizPage();
    } else if (currentPage === 'rush.html') {
        initRushPage();
    } else if (currentPage === 'result.html') {
        initResultPage();
    } else if (currentPage === 'module2.html') {
        initModule2Page();
    } else if (currentPage === 'module3.html') {
        initModule3Page();
    } else if (currentPage === 'module4.html') {
        initModule4Page();
    } else if (currentPage === 'module5.html') {
        initModule5Page();
    } else if (currentPage === 'module6.html') {
        initModule6Page();
    } else if (currentPage === 'module7.html') {
        initModule7Page();
    }
});

// 加载用户数据
function loadUsers() {
    const storedUsers = localStorage.getItem('quizUsers');
    if (storedUsers) {
        allUsers = JSON.parse(storedUsers);
    }
    
    // 加载当前用户
    const storedCurrentUser = localStorage.getItem('currentUser');
    if (storedCurrentUser) {
        currentUser = JSON.parse(storedCurrentUser);
    }
    
    // 加载当前答题
    const storedQuiz = localStorage.getItem('currentQuiz');
    if (storedQuiz) {
        currentQuiz = JSON.parse(storedQuiz);
    }
}

// 保存用户数据
function saveUsers() {
    localStorage.setItem('quizUsers', JSON.stringify(allUsers));
}

// 保存当前用户
function saveCurrentUser() {
    localStorage.setItem('currentUser', JSON.stringify(currentUser));
}

// 保存当前答题
function saveCurrentQuiz() {
    localStorage.setItem('currentQuiz', JSON.stringify(currentQuiz));
}

// 清除当前答题
function clearCurrentQuiz() {
    currentQuiz = null;
    localStorage.removeItem('currentQuiz');
}

// 初始化登录页面
function initLoginPage() {
    // 如果已有当前用户，预填充表单
    if (currentUser) {
        const usernameInput = document.getElementById('username');
        const birthdayInput = document.getElementById('birthday');
        
        if (usernameInput && currentUser.name) {
            usernameInput.value = currentUser.name;
        }
        
        if (birthdayInput && currentUser.birthday) {
            birthdayInput.value = currentUser.birthday;
        }
    }
    
    const startBtn = document.getElementById('start-btn');
    if (startBtn) {
        startBtn.addEventListener('click', () => {
            handleLogin(null, 'quiz.html');
        });
    }
    
    // 模块选择按钮
    const module1Btn = document.getElementById('module1-btn');
    if (module1Btn) {
        module1Btn.addEventListener('click', () => {
            handleLogin(null, 'quiz.html');
        });
    }
    
    const module2Btn = document.getElementById('module2-btn');
    if (module2Btn) {
        module2Btn.addEventListener('click', () => {
            handleLogin(null, 'module2.html');
        });
    }
    
    const module3Btn = document.getElementById('module3-btn');
    if (module3Btn) {
        module3Btn.addEventListener('click', () => {
            handleLogin(null, 'module3.html');
        });
    }
    
    const module4Btn = document.getElementById('module4-btn');
    if (module4Btn) {
        module4Btn.addEventListener('click', () => {
            handleLogin(null, 'module4.html');
        });
    }
    
    const module5Btn = document.getElementById('module5-btn');
    if (module5Btn) {
        module5Btn.addEventListener('click', () => {
            handleLogin(null, 'module5.html');
        });
    }
    
    const module6Btn = document.getElementById('module6-btn');
    if (module6Btn) {
        module6Btn.addEventListener('click', () => {
            handleLogin(null, 'module6.html');
        });
    }
    
    const module7Btn = document.getElementById('module7-btn');
    if (module7Btn) {
        module7Btn.addEventListener('click', () => {
            handleLogin(null, 'module7.html');
        });
    }
}

// 处理登录
function handleLogin(event, redirectPage = 'quiz.html') {
    if (event) {
        event.preventDefault();
    }
    const username = document.getElementById('username').value.trim();
    const birthday = document.getElementById('birthday').value;
    
    if (!username) {
        alert('请输入姓名');
        return;
    }
    
    if (!birthday) {
        alert('请选择入党生日');
        return;
    }
    
    // 检查用户是否已存在
    let user = allUsers.find(u => u.name === username);
    
    // 如果用户不存在，创建新用户
    if (!user) {
        user = {
            id: Date.now().toString(),
            name: username,
            birthday: birthday,
            quizzes: []
        };
        allUsers.push(user);
        saveUsers();
    }
    
    // 设置当前用户
    currentUser = user;
    saveCurrentUser();
    
    // 创建新的答题记录
    currentQuiz = {
        id: Date.now().toString(),
        userId: user.id,
        startTime: new Date().toISOString(),
        answers: [],
        totalScore: 0,
        completed: false
    };
    saveCurrentQuiz();
    
    // 跳转到指定页面
    window.location.href = redirectPage;
}

// 初始化答题页面
function initQuizPage() {
    // 检查是否已登录
    if (!currentUser) {
        window.location.href = 'index.html';
        return;
    }
    
    // 检查是否有进行中的答题
    if (!currentQuiz) {
        window.location.href = 'index.html';
        return;
    }
    
    // 显示用户信息
    const userInfo = document.getElementById('user-info');
    if (userInfo) {
        userInfo.textContent = `${currentUser.name} | 入党生日: ${formatDate(currentUser.birthday)}`;
    }
    
    // 获取题目
    const normalQuestions = getNormalQuestions(currentUser.birthday);
    
    // 显示第一题
    showQuestion(normalQuestions[currentQuestionIndex]);
    
    // 更新进度条
    updateProgress();
    
    // 开始计时器
    startTimer();
    
    // 绑定事件
    const submitBtn = document.getElementById('submit-btn');
    if (submitBtn) {
        submitBtn.addEventListener('click', () => handleSubmit(normalQuestions));
    }
    
    const exitBtn = document.getElementById('exit-btn');
    if (exitBtn) {
        exitBtn.addEventListener('click', showExitConfirmation);
    }
    
    const confirmExitBtn = document.getElementById('confirm-exit-btn');
    if (confirmExitBtn) {
        confirmExitBtn.addEventListener('click', exitQuiz);
    }
    
    const cancelExitBtn = document.getElementById('cancel-exit-btn');
    if (cancelExitBtn) {
        cancelExitBtn.addEventListener('click', hideExitConfirmation);
    }
    
    const nextBtn = document.getElementById('next-btn');
    if (nextBtn) {
        nextBtn.addEventListener('click', () => handleNext(normalQuestions));
    }
}

// 显示题目
function showQuestion(question) {
    const questionContent = document.getElementById('question-content');
    const optionsContainer = document.getElementById('options-container');
    
    if (questionContent && optionsContainer) {
        // 清空选项容器
        optionsContainer.innerHTML = '';
        
        // 显示题目内容
        questionContent.textContent = question.content;
        
        // 显示选项
        question.options.forEach((option, index) => {
            const optionItem = document.createElement('div');
            optionItem.className = 'option-item';
            optionItem.dataset.index = index;
            
            optionItem.innerHTML = `
                <input type="radio" name="answer" class="option-radio" id="option-${index}" value="${index}">
                <label for="option-${index}">${option}</label>
            `;
            
            optionItem.addEventListener('click', () => selectOption(index, optionItem));
            
            optionsContainer.appendChild(optionItem);
        });
    }
    
    // 重置选中的答案
    selectedAnswer = null;
}

// 选择选项
function selectOption(index, optionItem) {
    // 清除所有选项的选中状态
    const allOptions = document.querySelectorAll('.option-item');
    allOptions.forEach(item => item.classList.remove('selected'));
    
    // 设置当前选项为选中状态
    optionItem.classList.add('selected');
    
    // 更新选中的答案
    selectedAnswer = index;
    
    // 选中对应的单选按钮
    const radio = document.getElementById(`option-${index}`);
    if (radio) {
        radio.checked = true;
    }
}

// 更新进度条
function updateProgress() {
    const progressBar = document.getElementById('quiz-progress');
    const currentQuestionElement = document.getElementById('current-question');
    
    if (progressBar) {
        const progress = ((currentQuestionIndex + 1) / 3) * 100;
        progressBar.style.width = `${progress}%`;
    }
    
    if (currentQuestionElement) {
        currentQuestionElement.textContent = currentQuestionIndex + 1;
    }
}

// 开始计时器
function startTimer() {
    // 重置计时器
    clearInterval(timer);
    timerValue = 30;
    
    const timerElement = document.getElementById('timer');
    if (timerElement) {
        timerElement.textContent = timerValue;
    }
    
    // 启动计时器
    timer = setInterval(() => {
        timerValue--;
        
        if (timerElement) {
            timerElement.textContent = timerValue;
        }
        
        if (timerValue <= 0) {
            clearInterval(timer);
            handleTimeUp();
        }
    }, 1000);
}

// 处理时间到
function handleTimeUp() {
    const normalQuestions = getNormalQuestions(currentUser.birthday);
    handleSubmit(normalQuestions, true);
}

// 处理提交答案
function handleSubmit(questions, isTimeUp = false) {
    // 清除计时器
    clearInterval(timer);
    
    // 如果时间到但没有选择答案，则默认为未答
    if (isTimeUp && selectedAnswer === null) {
        selectedAnswer = -1;
    }
    
    // 如果没有选择答案，提示用户
    if (selectedAnswer === null) {
        alert('请选择一个答案');
        startTimer(); // 重新开始计时
        return;
    }
    
    const currentQuestion = questions[currentQuestionIndex];
    const isCorrect = selectedAnswer === currentQuestion.correctAnswer;
    
    // 计算得分
    const score = isCorrect ? 1 : 0;
    
    // 记录答案
    currentQuiz.answers.push({
        questionId: currentQuestion.id,
        userAnswer: selectedAnswer,
        isCorrect: isCorrect,
        score: score,
        isRushQuestion: false
    });
    
    // 更新总分
    currentQuiz.totalScore += score;
    
    // 保存当前答题
    saveCurrentQuiz();
    
    // 显示结果
    showResult(currentQuestion, isCorrect, score);
}

// 显示结果
function showResult(question, isCorrect, score) {
    const resultModal = document.getElementById('result-modal');
    const resultContent = document.getElementById('result-content');
    
    if (resultModal && resultContent) {
        // 构建结果内容
        let resultHTML = '';
        
        if (isCorrect) {
            resultHTML += `
                <div class="result-correct">
                    <div class="result-icon">✓</div>
                    <h3>回答正确</h3>
                    <p>+${score} 分</p>
                </div>
            `;
        } else {
            resultHTML += `
                <div class="result-wrong">
                    <div class="result-icon">✗</div>
                    <h3>回答错误</h3>
                    <p>+0 分</p>
                </div>
            `;
        }
        
        resultHTML += `
            <div class="result-explanation">
                <p><strong>正确答案：</strong>${question.options[question.correctAnswer]}</p>
                <p><strong>解析：</strong>${question.explanation}</p>
            </div>
        `;
        
        resultContent.innerHTML = resultHTML;
        
        // 显示结果弹窗
        resultModal.style.display = 'flex';
    }
}

// 处理下一题
function handleNext(questions) {
    // 隐藏结果弹窗
    const resultModal = document.getElementById('result-modal');
    if (resultModal) {
        resultModal.style.display = 'none';
    }
    
    // 增加题目索引
    currentQuestionIndex++;
    
    // 检查是否已完成所有普通题目
    if (currentQuestionIndex >= 3) {
        // 获取抢答题
        rushQuestions = getRushQuestions();
        
        // 跳转到抢答页面
        window.location.href = 'rush.html';
    } else {
        // 显示下一题
        showQuestion(questions[currentQuestionIndex]);
        
        // 更新进度条
        updateProgress();
        
        // 重新开始计时器
        startTimer();
    }
}

// 显示退出确认
function showExitConfirmation() {
    const exitModal = document.getElementById('exit-modal');
    if (exitModal) {
        exitModal.style.display = 'flex';
    }
}

// 隐藏退出确认
function hideExitConfirmation() {
    const exitModal = document.getElementById('exit-modal');
    if (exitModal) {
        exitModal.style.display = 'none';
    }
}

// 退出答题
function exitQuiz() {
    // 清除当前答题
    clearCurrentQuiz();
    
    // 跳转到首页
    window.location.href = 'index.html';
}

// 初始化抢答页面
function initRushPage() {
    // 检查是否已登录
    if (!currentUser) {
        window.location.href = 'index.html';
        return;
    }
    
    // 检查是否有进行中的答题
    if (!currentQuiz) {
        window.location.href = 'index.html';
        return;
    }
    
    // 显示用户信息
    const userInfo = document.getElementById('user-info');
    if (userInfo) {
        userInfo.textContent = `${currentUser.name} | 入党生日: ${formatDate(currentUser.birthday)}`;
    }
    
    // 如果没有抢答题，获取抢答题
    if (!rushQuestions || rushQuestions.length === 0) {
        rushQuestions = getRushQuestions();
    }
    
    // 显示第一个抢答题
    showRushQuestion(rushQuestions[currentRushIndex]);
    
    // 更新进度条
    updateRushProgress();
    
    // 绑定事件
    const rushBtn = document.getElementById('rush-btn');
    if (rushBtn) {
        rushBtn.addEventListener('click', handleRush);
    }
    
    const rushSubmitBtn = document.getElementById('rush-submit-btn');
    if (rushSubmitBtn) {
        rushSubmitBtn.addEventListener('click', handleRushSubmit);
    }
    
    const rushExitBtn = document.getElementById('rush-exit-btn');
    if (rushExitBtn) {
        rushExitBtn.addEventListener('click', showRushExitConfirmation);
    }
    
    const rushConfirmExitBtn = document.getElementById('rush-confirm-exit-btn');
    if (rushConfirmExitBtn) {
        rushConfirmExitBtn.addEventListener('click', exitQuiz);
    }
    
    const rushCancelExitBtn = document.getElementById('rush-cancel-exit-btn');
    if (rushCancelExitBtn) {
        rushCancelExitBtn.addEventListener('click', hideRushExitConfirmation);
    }
    
    const rushNextBtn = document.getElementById('rush-next-btn');
    if (rushNextBtn) {
        rushNextBtn.addEventListener('click', handleRushNext);
    }
}

// 显示抢答题
function showRushQuestion(question) {
    const questionContent = document.getElementById('rush-question-content');
    const optionsContainer = document.getElementById('rush-options-container');
    
    if (questionContent && optionsContainer) {
        // 清空选项容器
        optionsContainer.innerHTML = '';
        
        // 显示题目内容
        questionContent.textContent = question.content;
        
        // 显示选项
        question.options.forEach((option, index) => {
            const optionItem = document.createElement('div');
            optionItem.className = 'option-item';
            optionItem.dataset.index = index;
            
            optionItem.innerHTML = `
                <input type="radio" name="rush-answer" class="option-radio" id="rush-option-${index}" value="${index}">
                <label for="rush-option-${index}">${option}</label>
            `;
            
            optionItem.addEventListener('click', () => selectRushOption(index, optionItem));
            
            optionsContainer.appendChild(optionItem);
        });
    }
    
    // 重置选中的答案
    selectedAnswer = null;
    
    // 重置抢答状态
    isRushMode = false;
    rushWinner = null;
    hasRushed = false;
    isWaitingForWinner = false;
    
    // 显示抢答按钮，隐藏提交按钮
    const rushBtn = document.getElementById('rush-btn');
    const rushSubmitBtn = document.getElementById('rush-submit-btn');
    
    if (rushBtn && rushSubmitBtn) {
        rushBtn.style.display = 'block';
        rushSubmitBtn.style.display = 'none';
    }
    
    // 更新状态文本
    const statusText = document.getElementById('status-text');
    if (statusText) {
        statusText.textContent = '准备抢答（第一个抢答的人才能作答）';
    }
}

// 选择抢答选项
function selectRushOption(index, optionItem) {
    // 如果不是抢答成功者，不能选择
    if (!isRushMode || rushWinner !== currentUser.id) {
        return;
    }
    
    // 清除所有选项的选中状态
    const allOptions = document.querySelectorAll('.option-item');
    allOptions.forEach(item => item.classList.remove('selected'));
    
    // 设置当前选项为选中状态
    optionItem.classList.add('selected');
    
    // 更新选中的答案
    selectedAnswer = index;
    
    // 选中对应的单选按钮
    const radio = document.getElementById(`rush-option-${index}`);
    if (radio) {
        radio.checked = true;
    }
}

// 更新抢答进度条
function updateRushProgress() {
    const progressBar = document.getElementById('rush-progress');
    const currentRushElement = document.getElementById('current-rush');
    
    if (progressBar) {
        const progress = ((currentRushIndex + 1) / 2) * 100;
        progressBar.style.width = `${progress}%`;
    }
    
    if (currentRushElement) {
        currentRushElement.textContent = currentRushIndex + 1;
    }
}
// 处理抢答
function handleRush() {
    console.log('handleRush被调用');
    
    // 如果已经有人抢答成功或当前用户已经抢答过，则不允许再次抢答
    if (isRushMode || hasRushed || isWaitingForWinner) {
        console.log('抢答条件不满足:', { isRushMode, hasRushed, isWaitingForWinner });
        return;
    }
    
    // 标记当前用户已经抢答过
    hasRushed = true;
    console.log('标记用户已抢答');
    
    // 使用Socket.IO进行抢答
    if (socket) {
        console.log('使用Socket.IO发送抢答请求');
        
        // 发送抢答请求，包含房间ID、用户ID和用户名
        socket.emit('rush-attempt', {
            roomId: 'default-room',
            userId: currentUser.id,
            userName: currentUser.name
        });
        
        // 更新状态文本
        const statusText = document.getElementById('status-text');
        if (statusText) {
            statusText.textContent = '已发送抢答请求，等待服务器确认...';
        }
        
        // 设置等待状态
        isWaitingForWinner = true;
    } else {
        console.warn('Socket未连接，使用本地模式');
        
        // 本地模式下，第一个抢答的用户总是成功
        const isFirstToRush = !isRushMode;
        
        if (isFirstToRush) {
            // 设置为抢答模式
            isRushMode = true;
            
            // 设置当前用户为抢答成功者
            rushWinner = currentUser.id;
            
            // 更新状态文本
            const statusText = document.getElementById('status-text');
            if (statusText) {
                statusText.textContent = '抢答成功，请作答';
            }
            
            // 隐藏抢答按钮，显示提交按钮
            const rushBtn = document.getElementById('rush-btn');
            const rushSubmitBtn = document.getElementById('rush-submit-btn');
            
            if (rushBtn && rushSubmitBtn) {
                rushBtn.style.display = 'none';
                rushSubmitBtn.style.display = 'block';
            }
        } else {
            // 当前用户不是第一个抢答的，显示等待消息
            isWaitingForWinner = true;
            
            // 更新状态文本
            const statusText = document.getElementById('status-text');
            if (statusText) {
                statusText.textContent = '抢答未成功，请等待抢答者完成作答';
            }
            
            // 禁用抢答按钮
            const rushBtn = document.getElementById('rush-btn');
            if (rushBtn) {
                rushBtn.disabled = true;
            }
        }
    }
}

// 处理抢答提交
function handleRushSubmit() {
    console.log('handleRushSubmit被调用');
    
    // 如果没有选择答案，提示用户
    if (selectedAnswer === null) {
        alert('请选择一个答案');
        return;
    }
    
    const currentQuestion = rushQuestions[currentRushIndex];
    const isCorrect = selectedAnswer === currentQuestion.correctAnswer;
    
    // 计算得分（抢答题：答对+2分，答错-2分）
    const score = isCorrect ? 2 : -2;
    
    // 记录答案
    currentQuiz.answers.push({
        questionId: currentQuestion.id,
        userAnswer: selectedAnswer,
        isCorrect: isCorrect,
        score: score,
        isRushQuestion: true
    });
    
    // 更新总分
    currentQuiz.totalScore += score;
    
    // 保存当前答题
    saveCurrentQuiz();
    
    // 如果Socket连接可用，通知服务器答题完成
    if (socket) {
        console.log('使用Socket.IO发送答题完成通知');
        
        socket.emit('rush-answer-submitted', {
            roomId: 'default-room',
            userId: currentUser.id,
            userName: currentUser.name,
            questionId: currentQuestion.id,
            answer: selectedAnswer,
            isCorrect: isCorrect,
            score: score
        });
    }
    
    // 显示结果
    showRushResult(currentQuestion, isCorrect, score);
}


// 显示抢答结果
function showRushResult(question, isCorrect, score) {
    const resultModal = document.getElementById('rush-result-modal');
    const resultContent = document.getElementById('rush-result-content');
    
    if (resultModal && resultContent) {
        // 构建结果内容
        let resultHTML = '';
        
        if (isCorrect) {
            resultHTML += `
                <div class="result-correct">
                    <div class="result-icon">✓</div>
                    <h3>回答正确</h3>
                    <p>+${score} 分</p>
                </div>
            `;
        } else {
            resultHTML += `
                <div class="result-wrong">
                    <div class="result-icon">✗</div>
                    <h3>回答错误</h3>
                    <p>${score} 分</p>
                </div>
            `;
        }
        
        resultHTML += `
            <div class="result-explanation">
                <p><strong>正确答案：</strong>${question.options[question.correctAnswer]}</p>
                <p><strong>解析：</strong>${question.explanation}</p>
            </div>
        `;
        
        resultContent.innerHTML = resultHTML;
        
        // 显示结果弹窗
        resultModal.style.display = 'flex';
    }
}

// 处理抢答下一题
function handleRushNext() {
    // 隐藏结果弹窗
    const resultModal = document.getElementById('rush-result-modal');
    if (resultModal) {
        resultModal.style.display = 'none';
    }
    
    // 增加题目索引
    currentRushIndex++;
    
    // 检查是否已完成所有抢答题
    if (currentRushIndex >= 2) {
        // 保存当前答题状态（不标记为完成，因为还有模块二）
        saveCurrentQuiz();
        
        // 重置模块二相关变量
        currentModule2Index = 0;
        
        // 直接跳转到模块二页面
        window.location.href = 'module2.html';
    } else {
        // 显示下一题
        showRushQuestion(rushQuestions[currentRushIndex]);
        
        // 更新进度条
        updateRushProgress();
    }
}

// 非抢答者跳过当前抢答题
function skipCurrentRushQuestion() {
    // 如果当前用户是抢答成功者，不应该跳过
    if (rushWinner === currentUser.id) {
        return;
    }
    
    // 增加题目索引
    currentRushIndex++;
    
    // 检查是否已完成所有抢答题
    if (currentRushIndex >= 2) {
        // 保存当前答题状态（不标记为完成，因为还有模块二）
        saveCurrentQuiz();
        
        // 重置模块二相关变量
        currentModule2Index = 0;
        
        // 直接跳转到模块二页面
        window.location.href = 'module2.html';
    } else {
        // 显示下一题
        showRushQuestion(rushQuestions[currentRushIndex]);
        
        // 更新进度条
        updateRushProgress();
    }
}

// 显示抢答退出确认
function showRushExitConfirmation() {
    const exitModal = document.getElementById('rush-exit-modal');
    if (exitModal) {
        exitModal.style.display = 'flex';
    }
}

// 隐藏抢答退出确认
function hideRushExitConfirmation() {
    const exitModal = document.getElementById('rush-exit-modal');
    if (exitModal) {
        exitModal.style.display = 'none';
    }
}

// 初始化结果页面
function initResultPage() {
    // 检查是否已登录
    if (!currentUser) {
        window.location.href = 'index.html';
        return;
    }
    
    // 检查是否有完成的答题
    if (!currentQuiz || !currentQuiz.completed) {
        window.location.href = 'index.html';
        return;
    }
    
    // 显示用户信息
    const userInfo = document.getElementById('result-user-info');
    if (userInfo) {
        userInfo.textContent = `${currentUser.name} | 入党生日: ${formatDate(currentUser.birthday)}`;
    }
    
    // 显示总分
    const totalScoreElement = document.getElementById('total-score');
    if (totalScoreElement) {
        totalScoreElement.textContent = currentQuiz.totalScore;
    }
    
    // 显示普通题目正确数量
    const normalCorrectElement = document.getElementById('normal-correct');
    if (normalCorrectElement) {
        const normalCorrect = currentQuiz.answers.filter(a => !a.isRushQuestion && a.isCorrect).length;
        normalCorrectElement.textContent = normalCorrect;
    }
    
    // 显示抢答题目正确数量
    const rushCorrectElement = document.getElementById('rush-correct');
    if (rushCorrectElement) {
        const rushCorrect = currentQuiz.answers.filter(a => a.isRushQuestion && a.isCorrect).length;
        rushCorrectElement.textContent = rushCorrect;
    }
    
    // 显示模块二题目正确数量
    const module2CorrectElement = document.getElementById('module2-correct');
    if (module2CorrectElement) {
        const module2Correct = currentQuiz.answers.filter(a => a.isModule2Question && a.isCorrect).length;
        module2CorrectElement.textContent = module2Correct;
    }
    
    // 显示答题详情
    showAnswerDetails();
    
    // 显示排行榜
    showLeaderboard();
    
    // 绑定事件
    const restartBtn = document.getElementById('restart-btn');
    if (restartBtn) {
        restartBtn.addEventListener('click', restartQuiz);
    }
    
    const homeBtn = document.getElementById('home-btn');
    if (homeBtn) {
        homeBtn.addEventListener('click', goHome);
    }
}

// 显示答题详情
function showAnswerDetails() {
    const answerDetails = document.getElementById('answer-details');
    if (!answerDetails || !currentQuiz) return;
    
    // 清空答题详情容器
    answerDetails.innerHTML = '';
    
    // 获取所有题目
    const normalQuestions = getNormalQuestions(currentUser.birthday);
    
    // 显示答题详情
    currentQuiz.answers.forEach((answer, index) => {
        // 获取题目信息
        let question;
        if (answer.isModule2Question) {
            question = module2Questions.find(q => q.id === answer.questionId);
        } else if (answer.isRushQuestion) {
            question = rushQuestions.find(q => q.id === answer.questionId);
        } else {
            question = normalQuestions.find(q => q.id === answer.questionId);
        }
        
        if (!question) return;
        
        // 创建答题详情项
        const answerItem = document.createElement('div');
        answerItem.className = 'answer-item';
        
        // 构建答题详情内容
        answerItem.innerHTML = `
            <div>
                <span class="question-type ${answer.isRushQuestion ? 'rush' : answer.isModule2Question ? 'module2' : ''}">${answer.isRushQuestion ? '抢答' : answer.isModule2Question ? '模块二' : '普通'}</span>
                <span>${question.content}</span>
            </div>
            <div>
                <span class="answer-status ${answer.isCorrect ? 'correct' : 'wrong'}">${answer.isCorrect ? '正确' : '错误'}</span>
                <span>${answer.score > 0 ? '+' + answer.score : answer.score}</span>
            </div>
        `;
        
        answerDetails.appendChild(answerItem);
    });
}

// 显示排行榜
function showLeaderboard() {
    const rankings = document.getElementById('rankings');
    if (!rankings) return;
    
    // 清空排行榜容器
    rankings.innerHTML = '';
    
    // 获取所有用户的最高分
    const userScores = allUsers.map(user => {
        const highestScore = user.quizzes.length > 0 
            ? Math.max(...user.quizzes.map(q => q.totalScore))
            : 0;
        
        return {
            id: user.id,
            name: user.name,
            score: highestScore
        };
    });
    
    // 按分数降序排序
    userScores.sort((a, b) => b.score - a.score);
    
    // 显示排行榜
    userScores.forEach((user, index) => {
        const rankingItem = document.createElement('div');
        rankingItem.className = `ranking-item ${user.id === currentUser.id ? 'current-user' : ''}`;
        
        // 构建排名内容
        let rankHTML = '';
        if (index < 3) {
            const medalClass = index === 0 ? '' : (index === 1 ? 'silver' : 'bronze');
            rankHTML = `<span class="rank-medal ${medalClass}">${index + 1}</span>`;
        } else {
            rankHTML = `<span>${index + 1}</span>`;
        }
        
        rankingItem.innerHTML = `
            <div class="rank-column">${rankHTML}</div>
            <div class="name-column">${user.name}</div>
            <div class="score-column">${user.score}</div>
        `;
        
        rankings.appendChild(rankingItem);
    });
}

// 初始化模块二页面
function initModule2Page() {
    // 检查是否已登录
    if (!currentUser) {
        window.location.href = 'index.html';
        return;
    }
    
    // 检查是否有进行中的答题
    if (!currentQuiz) {
        window.location.href = 'index.html';
        return;
    }
    
    // 显示用户信息
    const userInfo = document.getElementById('user-info');
    if (userInfo) {
        userInfo.textContent = `${currentUser.name} | 入党生日: ${formatDate(currentUser.birthday)}`;
    }
    
    // 获取模块二题目
    module2Questions = getModule2Questions();
    
    // 显示第一题
    showModule2Question(module2Questions[currentModule2Index]);
    
    // 更新进度条
    updateModule2Progress();
    
    // 开始计时器
    startTimer();
    
    // 绑定事件
    const submitBtn = document.getElementById('module2-submit-btn');
    if (submitBtn) {
        submitBtn.addEventListener('click', handleModule2Submit);
    }
    
    const exitBtn = document.getElementById('module2-exit-btn');
    if (exitBtn) {
        exitBtn.addEventListener('click', showModule2ExitConfirmation);
    }
    
    const confirmExitBtn = document.getElementById('module2-confirm-exit-btn');
    if (confirmExitBtn) {
        confirmExitBtn.addEventListener('click', exitQuiz);
    }
    
    const cancelExitBtn = document.getElementById('module2-cancel-exit-btn');
    if (cancelExitBtn) {
        cancelExitBtn.addEventListener('click', hideModule2ExitConfirmation);
    }
    
    const nextBtn = document.getElementById('module2-next-btn');
    if (nextBtn) {
        nextBtn.addEventListener('click', handleModule2Next);
    }
}

// 显示模块二题目
function showModule2Question(question) {
    const questionContent = document.getElementById('module2-question-content');
    const optionsContainer = document.getElementById('module2-options-container');
    const imageContainer = document.getElementById('question-image-container');
    
    if (questionContent && optionsContainer && imageContainer) {
        // 清空选项容器和图片容器
        optionsContainer.innerHTML = '';
        imageContainer.innerHTML = '';
        
        // 显示题目图片
        const img = document.createElement('img');
        img.src = question.imageUrl;
        img.alt = '历史图片';
        img.className = 'question-image';
        imageContainer.appendChild(img);
        
        // 显示题目内容
        questionContent.textContent = question.content;
        
        // 显示选项
        question.options.forEach((option, index) => {
            const optionItem = document.createElement('div');
            optionItem.className = 'option-item';
            optionItem.dataset.index = index;
            
            optionItem.innerHTML = `
                <input type="radio" name="module2-answer" class="option-radio" id="module2-option-${index}" value="${index}">
                <label for="module2-option-${index}">${option}</label>
            `;
            
            optionItem.addEventListener('click', () => selectModule2Option(index, optionItem));
            
            optionsContainer.appendChild(optionItem);
        });
    }
    
    // 重置选中的答案
    selectedAnswer = null;
}

// 选择模块二选项
function selectModule2Option(index, optionItem) {
    // 清除所有选项的选中状态
    const allOptions = document.querySelectorAll('.option-item');
    allOptions.forEach(item => item.classList.remove('selected'));
    
    // 设置当前选项为选中状态
    optionItem.classList.add('selected');
    
    // 更新选中的答案
    selectedAnswer = index;
    
    // 选中对应的单选按钮
    const radio = document.getElementById(`module2-option-${index}`);
    if (radio) {
        radio.checked = true;
    }
}

// 更新模块二进度条
function updateModule2Progress() {
    const progressBar = document.getElementById('module2-progress');
    const currentQuestionElement = document.getElementById('current-module2-question');
    
    if (progressBar) {
        const progress = ((currentModule2Index + 1) / 5) * 100;
        progressBar.style.width = `${progress}%`;
    }
    
    if (currentQuestionElement) {
        currentQuestionElement.textContent = currentModule2Index + 1;
    }
}

// 处理模块二提交
function handleModule2Submit() {
    // 清除计时器
    clearInterval(timer);
    
    // 如果没有选择答案，提示用户
    if (selectedAnswer === null) {
        alert('请选择一个答案');
        startTimer(); // 重新开始计时
        return;
    }
    
    const currentQuestion = module2Questions[currentModule2Index];
    const isCorrect = selectedAnswer === currentQuestion.correctAnswer;
    
    // 计算得分（答对+1分，答错-1分）
    const score = isCorrect ? 1 : -1;
    
    // 记录答案
    currentQuiz.answers.push({
        questionId: currentQuestion.id,
        userAnswer: selectedAnswer,
        isCorrect: isCorrect,
        score: score,
        isModule2Question: true
    });
    
    // 更新总分
    currentQuiz.totalScore += score;
    
    // 保存当前答题
    saveCurrentQuiz();
    
    // 显示结果
    showModule2Result(currentQuestion, isCorrect, score);
}

// 显示模块二结果
function showModule2Result(question, isCorrect, score) {
    const resultModal = document.getElementById('module2-result-modal');
    const resultContent = document.getElementById('module2-result-content');
    
    if (resultModal && resultContent) {
        // 构建结果内容
        let resultHTML = '';
        
        if (isCorrect) {
            resultHTML += `
                <div class="result-correct">
                    <div class="result-icon">✓</div>
                    <h3>回答正确</h3>
                    <p>+${score} 分</p>
                </div>
            `;
        } else {
            resultHTML += `
                <div class="result-wrong">
                    <div class="result-icon">✗</div>
                    <h3>回答错误</h3>
                    <p>${score} 分</p>
                </div>
            `;
        }
        
        resultHTML += `
            <div class="result-explanation">
                <p><strong>正确答案：</strong>${question.options[question.correctAnswer]}</p>
                <p><strong>解析：</strong>${question.explanation}</p>
            </div>
        `;
        
        resultContent.innerHTML = resultHTML;
        
        // 显示结果弹窗
        resultModal.style.display = 'flex';
    }
}

// 处理模块二下一题
function handleModule2Next() {
    // 隐藏结果弹窗
    const resultModal = document.getElementById('module2-result-modal');
    if (resultModal) {
        resultModal.style.display = 'none';
    }
    
    // 增加题目索引
    currentModule2Index++;
    
    // 检查是否已完成所有模块二题目
    if (currentModule2Index >= 5) {
        // 标记答题完成（所有模块都已完成）
        currentQuiz.completed = true;
        currentQuiz.endTime = new Date().toISOString();
        
        // 保存当前答题
        saveCurrentQuiz();
        
        // 保存到用户的答题记录
        const userIndex = allUsers.findIndex(u => u.id === currentUser.id);
        if (userIndex !== -1) {
            allUsers[userIndex].quizzes.push(currentQuiz);
            saveUsers();
        }
        
        // 重置模块三相关变量
        currentModule3Index = 0;
        
        // 直接跳转到模块三页面
        window.location.href = 'module3.html';
    } else {
        // 显示下一题
        showModule2Question(module2Questions[currentModule2Index]);
        
        // 更新进度条
        updateModule2Progress();
        
        // 重新开始计时器
        startTimer();
    }
}

// 显示模块二退出确认
function showModule2ExitConfirmation() {
    const exitModal = document.getElementById('module2-exit-modal');
    if (exitModal) {
        exitModal.style.display = 'flex';
    }
}

// 隐藏模块二退出确认
function hideModule2ExitConfirmation() {
    const exitModal = document.getElementById('module2-exit-modal');
    if (exitModal) {
        exitModal.style.display = 'none';
    }
}

// 重新答题
function restartQuiz() {
    // 清除当前答题
    clearCurrentQuiz();
    
    // 创建新的答题记录
    currentQuiz = {
        id: Date.now().toString(),
        userId: currentUser.id,
        startTime: new Date().toISOString(),
        answers: [],
        totalScore: 0,
        completed: false
    };
    saveCurrentQuiz();
    
    // 重置题目索引
    currentQuestionIndex = 0;
    currentRushIndex = 0;
    
    // 跳转到答题页面
    window.location.href = 'quiz.html';
}

// 返回首页
function goHome() {
    // 清除当前答题
    clearCurrentQuiz();
    
    // 跳转到首页
    window.location.href = 'index.html';
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    
    return `${year}年${month}月${day}日`;
}

// 当页面加载完成后，检查是否需要替换标题
document.addEventListener('DOMContentLoaded', function() {
    // 在rush.html页面添加一个定时器，检查是否有人抢答成功
    const currentPage = window.location.pathname.split('/').pop();
    if (currentPage === 'rush.html') {
        // 每秒检查一次抢答状态
        setInterval(() => {
            // 如果当前用户不是抢答成功者，且有人抢答成功，且当前用户没有跳过当前题目
            if (isRushMode && rushWinner !== currentUser.id && isWaitingForWinner) {
                // 显示等待消息
                const statusText = document.getElementById('status-text');
                if (statusText) {
                    statusText.textContent = '其他人抢答成功，请等待抢答者完成作答';
                }
                
                // 禁用抢答按钮
                const rushBtn = document.getElementById('rush-btn');
                if (rushBtn) {
                    rushBtn.disabled = true;
                }
            }
        }, 1000);
    }
});

// 初始化模块三页面
function initModule3Page() {
    // 检查是否已登录
    if (!currentUser) {
        window.location.href = 'index.html';
        return;
    }
    
    // 检查是否有进行中的答题
    if (!currentQuiz) {
        window.location.href = 'index.html';
        return;
    }
    
    // 显示用户信息
    const userInfo = document.getElementById('user-info');
    if (userInfo) {
        userInfo.textContent = `${currentUser.name} | 入党生日: ${formatDate(currentUser.birthday)}`;
    }
    
    // 获取模块三题目
    module3Questions = getModule3Questions();
    
    // 显示第一题
    showModule3Question(module3Questions[currentModule3Index]);
    
    // 更新进度条
    updateModule3Progress();
    
    // 绑定事件
    const rushBtn = document.getElementById('module3-rush-btn');
    if (rushBtn) {
        rushBtn.addEventListener('click', handleModule3Rush);
    }
    
    const submitBtn = document.getElementById('module3-submit-btn');
    if (submitBtn) {
        submitBtn.addEventListener('click', handleModule3Submit);
    }
    
    const exitBtn = document.getElementById('module3-exit-btn');
    if (exitBtn) {
        exitBtn.addEventListener('click', showModule3ExitConfirmation);
    }
    
    const confirmExitBtn = document.getElementById('module3-confirm-exit-btn');
    if (confirmExitBtn) {
        confirmExitBtn.addEventListener('click', exitQuiz);
    }
    
    const cancelExitBtn = document.getElementById('module3-cancel-exit-btn');
    if (cancelExitBtn) {
        cancelExitBtn.addEventListener('click', hideModule3ExitConfirmation);
    }
    
    const nextBtn = document.getElementById('module3-next-btn');
    if (nextBtn) {
        nextBtn.addEventListener('click', handleModule3Next);
    }
}

// 显示模块三题目
function showModule3Question(question) {
    const questionContent = document.getElementById('module3-question-content');
    const optionsContainer = document.getElementById('module3-options-container');
    const audioContainer = document.getElementById('question-audio-container');
    
    if (questionContent && optionsContainer && audioContainer) {
        // 清空选项容器和音频容器
        optionsContainer.innerHTML = '';
        audioContainer.innerHTML = '';
        
        // 显示题目音频
        const audio = document.createElement('audio');
        audio.controls = true;
        audio.src = question.audioUrl;
        audio.className = 'question-audio';
        audioContainer.appendChild(audio);
        
        // 显示题目内容
        questionContent.textContent = question.content;
        
        // 显示选项
        question.options.forEach((option, index) => {
            const optionItem = document.createElement('div');
            optionItem.className = 'option-item';
            optionItem.dataset.index = index;
            
            optionItem.innerHTML = `
                <input type="radio" name="module3-answer" class="option-radio" id="module3-option-${index}" value="${index}">
                <label for="module3-option-${index}">${option}</label>
            `;
            
            optionItem.addEventListener('click', () => selectModule3Option(index, optionItem));
            
            optionsContainer.appendChild(optionItem);
        });
    }
    
    // 重置选中的答案
    selectedAnswer = null;
    
    // 重置抢答状态
    isModule3RushMode = false;
    module3RushWinner = null;
    hasModule3Rushed = false;
    isWaitingForModule3Winner = false;
    
    // 显示抢答按钮，隐藏提交按钮
    const rushBtn = document.getElementById('module3-rush-btn');
    const submitBtn = document.getElementById('module3-submit-btn');
    
    if (rushBtn && submitBtn) {
        rushBtn.style.display = 'block';
        submitBtn.style.display = 'none';
    }
    
    // 更新状态文本
    const statusText = document.getElementById('status-text');
    if (statusText) {
        statusText.textContent = '准备抢答红歌题（第一个抢答的人才能作答）';
    }
}

// 选择模块三选项
function selectModule3Option(index, optionItem) {
    // 如果不是抢答成功者，不能选择
    if (!isModule3RushMode || module3RushWinner !== currentUser.id) {
        return;
    }
    
    // 清除所有选项的选中状态
    const allOptions = document.querySelectorAll('.option-item');
    allOptions.forEach(item => item.classList.remove('selected'));
    
    // 设置当前选项为选中状态
    optionItem.classList.add('selected');
    
    // 更新选中的答案
    selectedAnswer = index;
    
    // 选中对应的单选按钮
    const radio = document.getElementById(`module3-option-${index}`);
    if (radio) {
        radio.checked = true;
    }
}

// 更新模块三进度条
function updateModule3Progress() {
    const progressBar = document.getElementById('module3-progress');
    const currentQuestionElement = document.getElementById('current-module3-question');

    if (progressBar) {
        const progress = ((currentModule3Index + 1) / 3) * 100;
        progressBar.style.width = `${progress}%`;
    }

    if (currentQuestionElement) {
        currentQuestionElement.textContent = currentModule3Index + 1;
    }
}

// 处理模块三抢答
function handleModule3Rush() {
    console.log('handleModule3Rush被调用');
    
    // 如果已经有人抢答成功或当前用户已经抢答过，则不允许再次抢答
    if (isModule3RushMode || hasModule3Rushed || isWaitingForModule3Winner) {
        console.log('模块三抢答条件不满足:', { isModule3RushMode, hasModule3Rushed, isWaitingForModule3Winner });
        return;
    }
    
    // 标记当前用户已经抢答过
    hasModule3Rushed = true;
    console.log('标记用户已在模块三抢答');
    
    // 使用Socket.IO进行抢答
    if (socket) {
        console.log('使用Socket.IO发送模块三抢答请求');
        
        // 发送抢答请求，包含房间ID、用户ID和用户名
        socket.emit('module3-rush-attempt', {
            roomId: 'module3-room',
            userId: currentUser.id,
            userName: currentUser.name
        });
        
        // 更新状态文本
        const statusText = document.getElementById('status-text');
        if (statusText) {
            statusText.textContent = '已发送抢答请求，等待服务器确认...';
        }
        
        // 设置等待状态
        isWaitingForModule3Winner = true;
    } else {
        console.warn('Socket未连接，使用本地模式');
        
        // 本地模式下，第一个抢答的用户总是成功
        const isFirstToRush = !isModule3RushMode;
        
        if (isFirstToRush) {
            // 设置为抢答模式
            isModule3RushMode = true;
            
            // 设置当前用户为抢答成功者
            module3RushWinner = currentUser.id;
            
            // 更新状态文本
            const statusText = document.getElementById('status-text');
            if (statusText) {
                statusText.textContent = '抢答成功，请作答';
            }
            
            // 隐藏抢答按钮，显示提交按钮
            const rushBtn = document.getElementById('module3-rush-btn');
            const submitBtn = document.getElementById('module3-submit-btn');
            
            if (rushBtn && submitBtn) {
                rushBtn.style.display = 'none';
                submitBtn.style.display = 'block';
            }
            
            // 播放音频
            const audioElement = document.querySelector('.question-audio');
            if (audioElement) {
                audioElement.play();
            }
        } else {
            // 当前用户不是第一个抢答的，显示等待消息
            isWaitingForModule3Winner = true;
            
            // 更新状态文本
            const statusText = document.getElementById('status-text');
            if (statusText) {
                statusText.textContent = '抢答未成功，请等待抢答者完成作答';
            }
            
            // 禁用抢答按钮
            const rushBtn = document.getElementById('module3-rush-btn');
            if (rushBtn) {
                rushBtn.disabled = true;
            }
        }
    }
}

// 处理模块三提交
function handleModule3Submit() {
    console.log('handleModule3Submit被调用');
    
    // 如果没有选择答案，提示用户
    if (selectedAnswer === null) {
        alert('请选择一个答案');
        return;
    }
    
    const currentQuestion = module3Questions[currentModule3Index];
    const isCorrect = selectedAnswer === currentQuestion.correctAnswer;
    
    // 计算得分（红歌竞猜：答对+2分，答错-2分）
    const score = isCorrect ? 2 : -2;
    
    // 记录答案
    currentQuiz.answers.push({
        questionId: currentQuestion.id,
        userAnswer: selectedAnswer,
        isCorrect: isCorrect,
        score: score,
        isModule3Question: true
    });
    
    // 更新总分
    currentQuiz.totalScore += score;
    
    // 保存当前答题
    saveCurrentQuiz();
    
    // 如果Socket连接可用，通知服务器答题完成
    if (socket) {
        console.log('使用Socket.IO发送模块三答题完成通知');
        
        socket.emit('module3-answer-submitted', {
            roomId: 'module3-room',
            userId: currentUser.id,
            userName: currentUser.name,
            questionId: currentQuestion.id,
            answer: selectedAnswer,
            isCorrect: isCorrect,
            score: score
        });
    }
    
    // 显示结果
    showModule3Result(currentQuestion, isCorrect, score);
}

// 显示模块三结果
function showModule3Result(question, isCorrect, score) {
    const resultModal = document.getElementById('module3-result-modal');
    const resultContent = document.getElementById('module3-result-content');
    
    if (resultModal && resultContent) {
        // 构建结果内容
        let resultHTML = '';
        
        if (isCorrect) {
            resultHTML += `
                <div class="result-correct">
                    <div class="result-icon">✓</div>
                    <h3>回答正确</h3>
                    <p>+${score} 分</p>
                </div>
            `;
        } else {
            resultHTML += `
                <div class="result-wrong">
                    <div class="result-icon">✗</div>
                    <h3>回答错误</h3>
                    <p>${score} 分</p>
                </div>
            `;
        }
        
        resultHTML += `
            <div class="result-explanation">
                <p><strong>正确答案：</strong>${question.options[question.correctAnswer]}</p>
                <p><strong>解析：</strong>${question.explanation}</p>
            </div>
        `;
        
        resultContent.innerHTML = resultHTML;
        
        // 显示结果弹窗
        resultModal.style.display = 'flex';
    }
}

// 处理模块三下一题
function handleModule3Next() {
    // 隐藏结果弹窗
    const resultModal = document.getElementById('module3-result-modal');
    if (resultModal) {
        resultModal.style.display = 'none';
    }
    
    // 增加题目索引
    currentModule3Index++;
    
    // 检查是否已完成所有模块三题目
    if (currentModule3Index >= 3) {
        // 保存当前答题状态（不标记为完成，因为还有模块四）
        saveCurrentQuiz();
        
        // 跳转到模块四页面
        window.location.href = 'module4.html';
    } else {
        // 显示下一题
        showModule3Question(module3Questions[currentModule3Index]);
        
        // 更新进度条
        updateModule3Progress();
    }
}

// 显示模块三退出确认
function showModule3ExitConfirmation() {
    const exitModal = document.getElementById('module3-exit-modal');
    if (exitModal) {
        exitModal.style.display = 'flex';
    }
}

// 隐藏模块三退出确认
function hideModule3ExitConfirmation() {
    const exitModal = document.getElementById('module3-exit-modal');
    if (exitModal) {
        exitModal.style.display = 'none';
    }
}

// 初始化模块四页面
function initModule4Page() {
    // 检查是否已登录
    if (!currentUser) {
        window.location.href = 'index.html';
        return;
    }
    
    // 检查是否有进行中的答题
    if (!currentQuiz) {
        window.location.href = 'index.html';
        return;
    }
    
    // 显示用户信息
    const userInfo = document.getElementById('user-info');
    if (userInfo) {
        userInfo.textContent = `${currentUser.name} | 入党生日: ${formatDate(currentUser.birthday)}`;
    }
    
    // 模块四的游戏逻辑已在module4.html中通过内联脚本实现
}

// 初始化模块五页面
function initModule5Page() {
    // 检查是否已登录
    if (!currentUser) {
        window.location.href = 'index.html';
        return;
    }
    
    // 检查是否有进行中的答题
    if (!currentQuiz) {
        window.location.href = 'index.html';
        return;
    }
    
    // 显示用户信息
    const userInfo = document.getElementById('user-info');
    if (userInfo) {
        userInfo.textContent = `${currentUser.name} | 入党生日: ${formatDate(currentUser.birthday)}`;
    }
    
    // 模块五的游戏逻辑已在module5.html中通过内联脚本实现
}

// 初始化模块六页面
function initModule6Page() {
    // 检查是否已登录
    if (!currentUser) {
        window.location.href = 'index.html';
        return;
    }
    
    // 检查是否有进行中的答题
    if (!currentQuiz) {
        window.location.href = 'index.html';
        return;
    }
    
    // 显示用户信息
    const userInfo = document.getElementById('user-info');
    if (userInfo) {
        userInfo.textContent = `${currentUser.name} | 入党生日: ${formatDate(currentUser.birthday)}`;
    }
    
    // 模块六的游戏逻辑已在module6.html中通过内联脚本实现
}

// 初始化模块七页面
function initModule7Page() {
    // 检查是否已登录
    if (!currentUser) {
        window.location.href = 'index.html';
        return;
    }
    
    // 检查是否有进行中的答题
    if (!currentQuiz) {
        window.location.href = 'index.html';
        return;
    }
    
    // 显示用户信息
    const userInfo = document.getElementById('user-info');
    if (userInfo) {
        userInfo.textContent = `${currentUser.name} | 入党生日: ${formatDate(currentUser.birthday)}`;
    }
    
    // 模块七的游戏逻辑已在module7.html中通过内联脚本实现
}
