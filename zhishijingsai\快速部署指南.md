# 🚀 党史竞赛系统快速部署指南

## 📋 操作概览

1. **本地准备** → 2. **上传文件** → 3. **服务器部署** → 4. **测试验证**

---

## 第一步：本地准备部署包

在您的本地项目目录中执行：

```bash
# 进入项目目录
cd zhishijingsai

# 创建部署包
chmod +x deploy-package.sh
./deploy-package.sh
```

这将生成一个名为 `zhishijingsai-fixed-YYYYMMDD-HHMMSS.tar.gz` 的压缩包。

---

## 第二步：上传文件到服务器

### 方法A：使用SCP命令（推荐）

```bash
# 替换 your-server-ip 为您的服务器IP
# 替换 your-username 为您的用户名
scp zhishijingsai-fixed-*.tar.gz your-username@your-server-ip:/home/
scp server-deploy.sh your-username@your-server-ip:/home/
```

### 方法B：使用FTP工具

1. 打开您的FTP客户端（如FileZilla、WinSCP等）
2. 连接到您的华为云服务器
3. 上传以下文件到 `/home/<USER>
   - `zhishijingsai-fixed-*.tar.gz`
   - `server-deploy.sh`

### 方法C：使用华为云控制台

1. 登录华为云控制台
2. 进入云服务器ECS
3. 使用远程登录功能
4. 通过控制台上传文件

---

## 第三步：服务器端部署

### 3.1 连接到服务器

```bash
# SSH连接到服务器
ssh your-username@your-server-ip
```

### 3.2 执行部署脚本

```bash
# 进入上传目录
cd /home

# 给部署脚本执行权限
chmod +x server-deploy.sh

# 执行部署（需要sudo权限）
sudo ./server-deploy.sh
```

### 3.3 部署过程说明

脚本将自动执行以下操作：
1. ✅ 停止现有服务
2. ✅ 备份原项目文件
3. ✅ 解压部署包
4. ✅ 复制修复的文件
5. ✅ 设置正确的文件权限
6. ✅ 安装Node.js依赖
7. ✅ 配置Nginx
8. ✅ 启动所有服务
9. ✅ 验证部署结果

---

## 第四步：测试验证

### 4.1 检查服务状态

```bash
# 查看PM2服务状态
pm2 list

# 查看端口占用
netstat -tlnp | grep -E ':(80|3000|3001)'

# 运行诊断脚本
cd /home/<USER>
./diagnose.sh
```

### 4.2 功能测试

1. **访问主页**：
   ```
   http://your-server-ip
   ```

2. **测试登录和导航**：
   - 输入姓名和入党生日
   - 点击"开始答题"按钮
   - 验证是否能正常跳转到答题页面

3. **测试抢答功能**：
   - 进入模块一（基石考验）
   - 点击"抢答"按钮
   - 验证抢答机制是否正常

4. **测试红歌竞猜**：
   - 进入模块三（红歌竞猜）
   - 测试音频播放和抢答功能

---

## 🔧 故障排除

### 如果部署失败：

1. **查看详细错误**：
   ```bash
   sudo ./server-deploy.sh 2>&1 | tee deploy.log
   ```

2. **检查权限问题**：
   ```bash
   sudo chown -R www-data:www-data /home/<USER>
   ```

3. **重启服务**：
   ```bash
   cd /home/<USER>
   ./stop-services.sh
   ./start-services.sh
   ```

### 如果网页无法访问：

1. **检查防火墙**：
   ```bash
   sudo ufw status
   sudo ufw allow 80
   ```

2. **检查Nginx**：
   ```bash
   sudo systemctl status nginx
   sudo nginx -t
   ```

3. **查看日志**：
   ```bash
   sudo tail -f /var/log/nginx/error.log
   pm2 logs
   ```

---

## 📞 紧急恢复

如果新部署出现问题，可以快速恢复到备份：

```bash
# 停止当前服务
cd /home/<USER>
./stop-services.sh

# 恢复备份（替换时间戳）
sudo rm -rf /home/<USER>
sudo mv /home/<USER>/home/<USER>

# 重启服务
cd /home/<USER>
./start-services.sh
```

---

## ✅ 成功标志

部署成功后，您应该看到：

1. ✅ PM2显示两个运行中的服务：`quiz-backend` 和 `quiz-socket`
2. ✅ 端口80、3000、3001都有进程监听
3. ✅ 访问网页能正常显示登录界面
4. ✅ 点击"开始答题"能正常跳转
5. ✅ 抢答功能正常工作

---

## 📱 联系支持

如果遇到问题，请提供以下信息：

1. 部署脚本的输出日志
2. `./diagnose.sh` 的输出结果
3. 具体的错误现象描述

祝您部署顺利！🎉
