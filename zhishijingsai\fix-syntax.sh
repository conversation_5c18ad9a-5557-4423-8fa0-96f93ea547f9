#!/bin/bash

# JavaScript语法修复脚本

echo "=== JavaScript语法修复脚本 ==="

PROJECT_PATH="/home/<USER>"
SCRIPT_FILE="$PROJECT_PATH/script.js"

# 1. 备份原文件
echo "1. 备份原文件..."
sudo cp "$SCRIPT_FILE" "$SCRIPT_FILE.backup-$(date +%Y%m%d-%H%M%S)"

# 2. 检查当前语法错误
echo "2. 检查当前语法错误..."
echo "语法检查结果："
node -c "$SCRIPT_FILE" 2>&1 || echo "发现语法错误"

# 3. 修复常见的语法错误
echo ""
echo "3. 修复语法错误..."

# 创建临时修复文件
TEMP_FILE="/tmp/script_fixed.js"

# 使用sed修复多余的花括号和语法错误
sudo sed '
# 移除多余的单独花括号行
/^[[:space:]]*}[[:space:]]*$/d
# 移除多余的空行
/^[[:space:]]*$/N;/^\n$/d
# 修复函数定义后的多余花括号
s/^[[:space:]]*}[[:space:]]*$//g
' "$SCRIPT_FILE" > "$TEMP_FILE"

# 4. 验证修复后的语法
echo "4. 验证修复后的语法..."
if node -c "$TEMP_FILE" 2>/dev/null; then
    echo "✅ 语法修复成功！"
    sudo cp "$TEMP_FILE" "$SCRIPT_FILE"
    sudo chown www-data:www-data "$SCRIPT_FILE"
    sudo chmod 644 "$SCRIPT_FILE"
else
    echo "❌ 自动修复失败，需要手动修复"
    echo "错误详情："
    node -c "$TEMP_FILE"
fi

# 5. 清理临时文件
rm -f "$TEMP_FILE"

# 6. 最终验证
echo ""
echo "5. 最终验证..."
if node -c "$SCRIPT_FILE" 2>/dev/null; then
    echo "✅ script.js 语法正确"
    
    # 检查handleLogin函数
    if grep -q "function handleLogin" "$SCRIPT_FILE"; then
        echo "✅ handleLogin函数存在"
    else
        echo "❌ handleLogin函数丢失"
    fi
    
    # 重启nginx确保更新生效
    echo "重启Nginx..."
    sudo systemctl restart nginx
    
    echo ""
    echo "🎉 修复完成！请刷新浏览器页面测试。"
    echo "如果仍有问题，请按Ctrl+F5强制刷新页面。"
    
else
    echo "❌ 仍有语法错误："
    node -c "$SCRIPT_FILE"
    echo ""
    echo "需要手动修复，请查看备份文件："
    ls -la "$SCRIPT_FILE".backup-*
fi

echo ""
echo "=== 修复脚本执行完成 ==="
